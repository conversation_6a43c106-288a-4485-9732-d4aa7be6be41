#!/bin/bash

# Navigate to the user directory
cd user
# Install dependencies using yarn
yarn install
# Build the project
yarn build
# Start the project
# yarn start
# Restart the PM2 process
pm2 restart entrestudios.com

# Navigate to the admin directory
cd ../admin
# Install dependencies using yarn
yarn install
# Build the project
yarn build
# Start the project
# yarn start
# Restart the PM2 process
pm2 restart admin.entrestudios.com

# Navigate to the api directory
cd ../api
# Install dependencies using yarn
yarn install
# Build the project
yarn build
# Start the project
# yarn start
# Restart the PM2 process
pm2 restart api.entrestudios.com
