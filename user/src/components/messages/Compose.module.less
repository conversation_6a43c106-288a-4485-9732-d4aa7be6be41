@import '../../../style/vars.less';

.compose {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  position: absolute;
  width: 100%;
  bottom: 0;
  left: 0;
  padding: 10px;
  gap: 8px;
  background: var(--component-background);
  border-top: 1px solid var(--border-color-split);

  :global .toolbar-button {
    color: var(--text-color-secondary);
    margin-left: 15px;
    transition: color 0.3s ease;
  }

  :global .toolbar-button:hover {
    color: var(--primary-color);
  }

  &.custom {
    width: 96%;
    top: unset;
    left: 2%;
    bottom: 10px;
  }

  @media screen and (max-width: 576px) {
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: flex-start;
    gap: 8px;
    background: var(--component-background);

    .inp-price {
      order: 1;
      width: 70px;
      margin-right: auto;
    }

    .compose-input {
      order: 3;
      width: 100%;
      margin: 0;
      flex-basis: 100%;
    }

    .grp-icons {
      order: 2;
      padding: 0;
      margin-right: 8px;
      display: flex;
      align-items: center;

      &:last-child {
        margin-right: 0;
      }

      .grp-send {
        margin-left: auto;
      }
    }

    .send-button {
      order: 2;
    }
  }
}

.inp-price {
  height: 43px;
  display: flex;
  align-items: center;
  border-color: unset;
  border: none;
  max-width: 70px;
  cursor: pointer;
}

.compose-input {
  flex: 1;
  border: none;
  font-size: 14px;
  height: 43px;
  background: none;
  color: var(--text-color) !important;
  outline: thin;
  padding: 10px;
  background-color: var(--bg-secondary) !important;
  border-radius: 10px;

  &::placeholder {
    color: var(--text-color-tertiary) !important;
    opacity: 1 !important;
  }

  :global .ant-input {
    background-color: var(--bg-secondary) !important;
    color: var(--text-color) !important;
    border: none !important;

    &::placeholder {
      color: var(--text-color-tertiary) !important;
      opacity: 1 !important;
    }
  }

  // Additional specificity for dark mode
  :global(.dark-mode) & {
    background-color: var(--bg-secondary) !important;
    color: var(--text-color) !important;

    &::placeholder {
      color: var(--text-color-tertiary) !important;
      opacity: 1 !important;
    }

    :global .ant-input {
      background-color: var(--bg-secondary) !important;
      color: var(--text-color) !important;

      &::placeholder {
        color: var(--text-color-tertiary) !important;
        opacity: 1 !important;
      }
    }
  }
}

.compose-input::placeholder {
  color: var(--text-color-tertiary) !important;
  opacity: 1 !important;
}

.grp-icons {
  padding: 0 10px;
  cursor: pointer;
  position: relative;
  font-size: 18px;

  :global .anticon-send {
    font-size: 22px;
    color: var(--theme-color);
  }

  &.custom {
    display: flex;

    .anticon-send {
      margin-left: 15px;
    }
  }
}

.grp-emotions {
  .anticon {
    font-size: 22px;
    color: var(--color-text);
  }
}

.grp-file-icon {
  :global .avatar-uploader {
    .ant-upload {
      width: auto;
      height: auto;
      background: transparent;
      border: none;
      margin: 0;
    }
  }

  :global .ant-upload {
    font-size: 18px;
  }

  :global .ant-upload-select.ant-upload-select-picture-card .anticon {
    position: unset;
    top: unset;
    left: unset;
    color: var(--color-text);
    font-size: 22px;
    padding: 0;
    background: transparent;
  }
}

.grp-send {
  height: 43px;
  width: 43px;
  line-height: 43px;
  text-align: center;
  background-color: var(--theme-color);
  color: var(--color-white);
  border-radius: 50%;
  cursor: pointer;

  :global .anticon-send {
    font-size: 18px;
    color: var(--color-text);
    transform: rotate(-45deg);
  }

  &:hover {
    background: var(--primary-color);
  }
}

.grp-send-tip {
  padding: 0px;
  display: inline-block;
  vertical-align: middle;
  border: 0;
  box-shadow: none;
  font-size: 20px;

  &:hover {
    color: var(--text-color);
  }

  &:focus {
    color: var(--text-color);
    border-color: var(--text-color);
  }
}

.schedule-btn {
  padding: 0;
  display: inline-block;
  vertical-align: middle;
  border: 0;
  box-shadow: none;
  font-size: 20px;
  color: var(--theme-color);

  &:hover {
    color: var(--primary-color);
  }

  &:focus {
    color: var(--theme-color);
  }

  &:disabled:hover {
    background-color: transparent;
  }

  &:disabled {
    background-color: transparent;
  }
}

.scheduleForm {
  display: flex;
  gap: 16px;
  margin: 20px 0;

  .datePicker,
  .timePicker {
    flex: 1;
  }
}

@media (max-width: 576px) {
  .scheduleForm {
    flex-direction: column;
    gap: 12px;
  }
}

.scheduleStatusBar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 16px;
  background-color: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color-split);
  z-index: 1;

  .scheduleInfo {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--text-color-secondary);
    font-size: 14px;

    .anticon {
      color: var(--theme-color);
    }
  }

  .viewScheduledBtn {
    display: flex;
    align-items: center;
    gap: 4px;
    color: var(--theme-color);
    font-size: 14px;
    padding: 0;
    height: auto;

    &:hover {
      color: var(--primary-color);
    }

    .anticon {
      font-size: 12px;
    }
  }

  @media (max-width: 576px) {
    padding: 8px 12px;

    .scheduleInfo {
      font-size: 13px;
    }

    .viewScheduledBtn {
      font-size: 13px;
    }
  }
}

.scheduledListModal {
  :global(.ant-modal-body) {
    padding: 0;
  }
}

.scheduledList {
  overflow-y: auto;
  max-height: 80vh;

  @media screen and (max-width: 576px) {
    max-height: 70vh;
  }

  :global {
    .ant-list-item {
      padding: 16px;
      border-bottom: 1px solid var(--border-color-split);

      &:last-child {
        border-bottom: none;
      }
    }

    .ant-list-item-meta-title {
      margin-bottom: 4px;
      font-size: 14px;
    }

    .ant-list-item-meta-description {
      font-size: 13px;
      color: var(--text-color-secondary);
    }

    .ant-list-item-action {
      margin-left: 16px;
    }
  }

  .editMode {
    .editActions {
      display: flex;
      gap: 8px;
      margin-top: 12px;
    }

    .editInput {
      margin-top: 8px;
    }
  }

  .messageText {
    white-space: pre-wrap;
    word-break: break-word;
  }

  @media screen and (max-width: 576px) {
    .editMode {
      .editActions {
        margin-top: 8px;
      }

      button {
        padding: 4px 12px;
        font-size: 13px;
      }
    }
  }
}

.teaser-wrapper {
  position: relative;
  display: inline-block;

  .new-badge {
    position: absolute;
    bottom: -30px;
    right: -6px;
    background: var(--theme-color);
    color: var(--color-white);
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 10px;
    font-weight: 500;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    animation: pulseAnimation 2s infinite;
    z-index: 1;

    @media screen and (max-width: 576px) {
      bottom: -30px;
      right: -3px;
      font-size: 9px;
      padding: 1px 4px;
    }
  }
}

@keyframes pulseAnimation {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

.actionButton {
  padding: 0px !important;
}
