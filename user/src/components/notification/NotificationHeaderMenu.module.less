@import '../../../style/vars.less';

.notification-menu {
  width: 350px;
  @media screen and (max-width: @mobile-screen) {
    width: 300px;
  }
  a {
    float: right;
  }
  h3 {
    font-weight: bold;
    float: left;
    margin: 3px;
    color: var(--text-color);
  }
  :global .ant-menu-item {
    height: 75px;
    overflow: hidden;
    border-bottom: 1px solid var(--border-color-split);
    background: var(--component-background);
    color: var(--text-color);
    transition: all 0.2s ease;

    .ant-card-meta-title {
      overflow: hidden;
      text-overflow: ellipsis;
    }

    &:hover {
      background: var(--item-hover-bg);
      border-bottom-color: var(--border-color-base);
    }
  }

  :global .ant-menu-item:last-child {
    border-bottom: none;
  }

  :global .notification-item {
    display: flex;
    padding: 4px 0;

    .notification-item-list {
      line-height: 18px;

      .message {
        white-space: normal;
        line-height: 20px;
        color: var(--text-color);
        font-size: 14px;
        margin-bottom: 2px;
      }

      .sub-message {
        color: var(--text-color-secondary);
        font-size: 13px;
        line-height: 18px;
        margin-bottom: 4px;
      }

      .time {
        line-height: 20px;
        color: var(--text-color-secondary);
        small {
          font-size: 12px;
          color: var(--text-color-secondary);
        }
      }
    }

    .notification-docw {
      position: absolute;
      width: 10px;
      height: 10px;
      background: var(--primary-color);
      right: 8px;
      border-radius: 50%;
      top: 25px;
      box-shadow: 0 0 0 2px var(--component-background);
    }
  }

  :global .notification-unread {
    background: rgba(139, 109, 209, 0.08);
    border-left: 3px solid var(--primary-color);

    .message {
      font-weight: 600;
      color: var(--text-color);
    }

    .sub-message {
      color: var(--text-color-secondary);
      font-weight: 500;
    }
  }

  :global .notification-read {
    .message {
      color: var(--text-color-secondary);
      font-weight: normal;
    }

    .sub-message {
      color: var(--text-color-tertiary);
    }

    .time {
      color: var(--text-color-tertiary);
      small {
        color: var(--text-color-tertiary);
      }
    }
  }
  :global .ant-dropdown-menu-item-group-title {
    border-bottom: 1px solid var(--border-color-base);
    overflow: hidden;
    padding: 8px 15px;
    background: var(--component-background);
    color: var(--text-color);
    font-weight: 600;
  }
  :global .ant-dropdown-menu-item-group-list {
    padding: 4px;
    max-height: calc(100vh - 150px);
    overflow-y: auto;
    margin: 0;
    margin-bottom: 37px;
    background: var(--component-background);
    border: 1px solid var(--border-color-base);
    border-radius: 6px;
    box-shadow: var(--shadow-2);
  }
  :global .ant-dropdown-menu-item {
    border-radius: 4px;
    padding: 8px;
    margin: 2px 4px;
    background: transparent;
    color: var(--text-color);
    transition: all 0.2s ease;

    &:hover {
      background: var(--item-hover-bg);
      border-color: var(--border-color-base);
    }
  }
  :global .ant-row .ant-col {
    padding-top: 0;
    padding-bottom: 0;
    flex: none;
    max-width: none;
    &.ant-col-md-3 {
      padding-right: 0 !important;
      width: 40px;
    }
    &.ant-col-md-21 {
      padding-right: 25px !important;
      width: calc(100% - 40px);
    }
  }
}

.btn-dismiss-read-all {
  padding: 6px 12px;
  border: 1px solid var(--border-color-base);
  color: var(--primary-color);
  background: transparent;
  float: right;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.2s ease;

  &:hover {
    border-color: var(--primary-color);
    background: var(--item-hover-bg);
    color: var(--primary-color);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(139, 109, 209, 0.2);
  }
  &:focus {
    border-color: var(--primary-color);
    background: var(--item-hover-bg);
    color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 2px rgba(139, 109, 209, 0.2);
  }
}
.see-all-notification {
  position: absolute;
  bottom: 0;
  width: 100%;
  left: 0;
  text-align: center;
  padding: 12px;
  background-color: var(--component-background);
  border-top: 1px solid var(--border-color-base);
  font-weight: 600;
  color: var(--primary-color);
  text-decoration: none;
  transition: all 0.2s ease;
  font-size: 13px;

  &:hover {
    background-color: var(--item-hover-bg);
    color: var(--primary-color);
    text-decoration: none;
    border-top-color: var(--primary-color);
  }

  &:focus {
    outline: none;
    box-shadow: inset 0 0 0 2px rgba(139, 109, 209, 0.3);
  }
}
