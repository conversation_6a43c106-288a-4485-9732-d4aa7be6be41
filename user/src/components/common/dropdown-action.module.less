.dropbtn {
  padding: 5px;
  border: none;
  border: 1px solid var(--border-color-base);
  background: var(--component-background);
  color: var(--text-color);
  transition: all 0.3s ease;
}

.dropbtn:hover {
  border-color: var(--primary-color);
  background: var(--bg-hover);
}

.dropdown {
  position: relative;
  display: inline-block;
}

.dropdown:hover {
  .dropdown-content {
    display: block;
  }
}

.dropdown-content {
  display: none;
  position: absolute;
  background-color: var(--component-background);
  min-width: 160px;
  left: -30px;
  box-shadow: var(--shadow-2);
  z-index: 999;
  border: 1px solid var(--border-color-base);
  border-radius: 8px;
  overflow: hidden;

  :global {
    a {
      color: var(--text-color);
      padding: 10px;
      text-decoration: none;
      display: block;
      transition: all 0.2s ease;
    }

    a:hover {
      background-color: var(--bg-hover);
      color: var(--primary-color);
    }
  }
}

.space-item {
  float: left;
}
