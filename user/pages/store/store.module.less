@import '../../style/vars.less';

.product-card {
  display: flex;
  margin: 0 0 10px;

  :global .prod-img {
    width: 40%;
    position: relative;

    .ant-image {
      width: 100%;

      img {
        width: 100%;
      }
    }

    .prod-stock {
      position: absolute;
      right: auto;
      left: 0px;
      top: 10px;
      color: var(--color-white);
      line-height: 2em;
      background-color: var(--primary-color);
      padding: 0.2em 2em;
      clip-path: polygon(0px 0px, 0px 100%, 100% 100%, 90% 50%, 100% 0);
      font-size: 12px;
      font-weight: 600;
      margin: 5px 0;
    }
  }

  :global .prod-info {
    width: 100%;
    padding: 0 20px;

    .prod-desc {
      font-size: 13px;
      max-height: 335px;
      padding: 5px 0;
      overflow: auto;
      color: var(--text-color-secondary);

      &::first-letter {
        text-transform: uppercase;
        white-space: pre-line;
      }
    }
  }

  @media screen and (max-width: @mobile-screen) {
    flex-direction: column;

    .prod-img {
      width: 100%;
    }
  }
}

.prod-price {
  font-size: 25px;
  color: var(--primary-color);
  font-weight: 600;
  font-weight: bold;
  margin: 10px 0;

  :global .dc-price {
    text-decoration: line-through;
    color: var(--error-color);
    font-size: 18px;
    margin-left: 5px;
  }
}

.actionIco {
  background: var(--bg-card) !important;
  border-color: var(--border-color-base) !important;
  color: var(--text-color-secondary) !important;

  &:hover {
    background: var(--bg-hover) !important;
    border-color: var(--primary-color) !important;
    color: var(--primary-color) !important;
  }
}
