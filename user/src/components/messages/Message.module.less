@import '../../../style/vars.less';

.message {
  display: flex;
  flex-direction: column;
}

.message .timestamp {
  display: flex;
  justify-content: flex-start;
  color: var(--text-color-tertiary);
  font-weight: 600;
  font-size: 8px;
  margin-bottom: 5px;
}

.message.mine .timestamp {
  justify-content: flex-end;
}

.message .bubble-container {
  font-size: 14px;
  display: flex;
  margin-bottom: 5px;
}

.message.mine .bubble-container {
  justify-content: flex-end;
}

.message .bubble-container .bubble {
  padding: 4px 4px;
  border-radius: 10px;
  // max-width: 75%;
  overflow-wrap: break-word;
  color: var(--text-message-other);
  transition: background-color 0.3s ease, color 0.3s ease;
}

.bubbleisbought {
  font-size: 10px;
  color: var(--text-color-secondary);
}

.message .bubble-container .bubble-image {
  margin: 4px 0px;
  background: var(--theme-color);
  padding: 0;
  border-radius: 10px;
  // max-width: 75%;
  color: var(--color-white);
  overflow-wrap: break-word;

  [class~='ant-image-img'] {
    border-radius: 10px;
  }

  [class~='ant-image-mask'] {
    border-radius: 10px;
  }
}

.message .bubble-container {
  :global .sell-message:hover {
    cursor: pointer;
    background: var(--bg-hover);
  }
}

.message.mine .bubble-container .bubble {
  // max-width: 75%;
  color: var(--text-message-mine);
  overflow-wrap: break-word;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.message-time {
  font-size: 10px;
  margin: 0;
}

.bubble-container .avatar {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  margin-right: 5px;
}

.message.mine .bubble-container .avatar {
  margin: 6px 0 0 8px;
}

.image-grid,
.media-grid {
  display: grid;
  gap: 2px;
  width: 100%;
  max-width: 600px;

  &.grid-1 {
    grid-template-columns: 1fr;
    img,
    video {
      width: 110px !important;
      height: 110px !important;
    }
  }

  &.grid-2 {
    grid-template-columns: repeat(2, 1fr);

    .image-item,
    .video-item {
      aspect-ratio: 1;
      img,
      video {
        width: 110px !important;
        height: 110px !important;
      }
    }
  }

  &.grid-3 {
    grid-template-columns: repeat(2, 1fr);

    .image-item:first-child,
    .video-item:first-child {
      grid-column: span 2;
      aspect-ratio: 2.5;
    }

    .image-item,
    .video-item {
      aspect-ratio: 1;
      img,
      video {
        // max-height: 100% !important;
        height: 100px !important;
      }
    }
  }

  &.grid-4 {
    grid-template-columns: repeat(2, 1fr);

    .image-item,
    .video-item {
      aspect-ratio: 4/5;
      img,
      video {
        min-height: 150px !important;
        object-fit: cover;
      }
    }
  }

  &.grid-5,
  &.grid-6 {
    grid-template-columns: repeat(3, 1fr);

    .image-item,
    .video-item {
      aspect-ratio: 1;
    }
  }
}

.image-item,
.video-item {
  position: relative;
  overflow: hidden;
  border-radius: 8px;

  :global {
    .ant-image {
      width: 100%;
      height: 100%;
    }

    .ant-image-img {
      width: 100%;
      height: 100%;
      object-fit: contain;
      border-radius: 5px;
    }

    video {
      width: 100%;
      height: 100%;
      object-fit: contain;
      border-radius: 5px;
    }
  }

  .more-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;

    span {
      color: white;
      font-size: 24px;
      font-weight: bold;
    }
  }
}

.content-wrapper {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-width: 30%;
  background: var(--bg-message-mine);
  padding: 8px;
  border-radius: 5px;
  transition: background-color 0.3s ease;
  .lockOverlay {
    .mediaCount {
      display: flex !important;
      flex-direction: column !important;
      // gap: 16px;
      font-size: 16px;
      flex-direction: column !important;

      span {
        display: flex;
        align-items: center;
        // gap: 4px;
      }
    }
    .unlockButton {
      background: var(--primary-color) !important;
      color: var(--color-white);
      border: none;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s;
      width: 100%;

      &:hover {
        background: var(--primary-color-hover) !important;
        transform: scale(1.05);
      }
    }
  }

  .bubble-images {
    overflow: hidden;
  }
  .media-grid {
    .image-item,
    .video-item {
      img {
        width: 100%;
        height: 80px;
        object-fit: cover;
      }
      video {
        width: 100%;
        height: 80px;
        object-fit: cover;
      }
    }
    .image-item:only-child img {
      height: auto;
    }
    .image-item:only-child video {
      height: auto !important;
    }
  }
}

.carouselContainer {
  position: relative;
  width: 100%;
}

.carouselWrapper {
  width: 100%;
  position: relative;
}

.navButton {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: var(--text-color);
  font-size: 24px;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
  z-index: 10;

  &:hover {
    background: rgba(255, 255, 255, 0.3);
  }

  &:first-child {
    left: 0px; // Left button
  }

  &:last-child {
    right: 0px; // Right button
  }
}

.preview-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 500px;
  position: relative;

  .previewImage {
    height: 470px !important;
    object-fit: contain !important;
  }

  video {
    width: 100%;
    height: 100%;
    object-fit: contain;
    background-color: black;
    padding: 5px;
    position: relative;
  }

  .imageCounter {
    position: absolute;
    bottom: -30px;
    left: 10px;
    background: rgba(0, 0, 0, 0.5);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 14px;
    z-index: 10;
  }
}

.blurredContent {
  .centerLock {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    filter: none !important;
    font-size: 32px;
    background: rgba(0, 0, 0, 0.5);
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
  }
}

.locked {
  position: relative;
  overflow: hidden;

  .blurredContent {
    pointer-events: none;
    .text {
      filter: blur(8px);
      pointer-events: none;
      margin-bottom: 8px;
    }
    img,
    video {
      filter: blur(20px);
      pointer-events: none;
    }
  }

  .lockOverlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 12px;
    border-radius: 8px;
  }
}

.messageOptions {
  opacity: 0;
  transition: opacity 0.2s ease;
  z-index: 2;

  // Show on hover/touch
  .bubble-container:hover &,
  .bubble-container:focus-within & {
    opacity: 1;
  }
}

.optionsIcon {
  color: var(--text-color-secondary);

  &:hover {
    color: var(--primary-color);
    background: transparent;
  }

  // Remove default button styles
  padding: 4px 8px;
  border: none;
  box-shadow: none;

  &:focus {
    background: transparent;
  }
}

.ppvBadge {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  background: var(--gradient-primary);
  color: var(--color-white);
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 13px;
  font-weight: 500;
  align-self: flex-start;
  margin-bottom: 4px;
  box-shadow: var(--shadow-1);

  :global(.anticon) {
    font-size: 14px;
  }
}

.teaserButton {
  margin-top: 8px;
  border-radius: 20px;
  background: var(--bg-overlay);
  border: 1px solid var(--border-color-base);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;

  &:hover {
    background: var(--bg-white);
    transform: translateY(-1px);
    box-shadow: var(--shadow-2);
  }
}

.teaserModal {
  :global(.ant-modal-content) {
    background: transparent;
    box-shadow: none;
  }

  :global(.ant-modal-close) {
    top: 10px;
    right: 10px;
    color: white;
  }
}

.teaserPreview {
  display: flex;
  justify-content: center;
  align-items: center;
  max-height: 90vh;
  max-width: 90vw;

  .teaserImage {
    max-height: 90vh;
    max-width: 90vw;
    object-fit: contain;
    border-radius: 8px;
    box-shadow: 0 4px 24px rgba(0, 0, 0, 0.2);
  }

  .teaserVideo {
    max-height: 90vh;
    max-width: 90vw;
    border-radius: 8px;
    box-shadow: 0 4px 24px rgba(0, 0, 0, 0.2);
  }
}

@media screen and (max-width: 768px) {
  .image-grid,
  .media-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .image-item,
  .video-item {
    width: 100% !important; /* Đảm bảo ảnh/video hiển thị đủ rộng */
    height: auto;
  }
  .unlockButton {
    font-size: 14px;
    // padding: 10px 15px;
    width: 100%;
  }
  .bubble-container {
    padding: 8px;
  }
  .content-wrapper {
    max-width: 75%;
    gap: 5px;
    padding: 6px;
    font-size: 14px;
  }

  .messageOptions {
    opacity: 1;
  }

  .ppvBadge {
    font-size: 12px;
    padding: 3px 10px;

    :global(.anticon) {
      font-size: 12px;
    }
  }

  .teaserButton {
    width: 100%;
    margin-bottom: 8px;
  }
}
