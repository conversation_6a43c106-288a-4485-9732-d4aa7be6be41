@import '../../../style/vars.less';

.notification-page {
  position: relative;
  border: 1px solid var(--border-color-base);
  border-radius: 0 0 8px 8px;
  background: var(--card-background);
  box-shadow: var(--shadow-1);
  :global .ant-menu-item {
    height: 75px;
    overflow: hidden;
    border-bottom: 0.5px solid var(--border-color-split);
    margin: 0 !important;
    padding: 15px;
    background: var(--card-background);
    color: var(--text-color);
    transition: all 0.3s ease;

    &:active,
    &:hover {
      background-color: var(--item-hover-bg);
    }
    .ant-card-meta-title {
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  :global .ant-menu-item:last-child {
    border-bottom: none;
  }

  :global .ant-menu-item {
    .notification-item-list {
      line-height: 18px;

      .message {
        white-space: normal;
        line-height: 20px;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        -moz-line-clamp: 2;
        -moz-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: break-word;
        color: var(--text-color);
        font-size: 14px;
        margin-bottom: 4px;
      }

      .sub-message {
        color: var(--text-color-secondary);
        font-size: 13px;
        line-height: 18px;
        margin-bottom: 4px;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .time {
        line-height: 20px;
        color: var(--text-color-secondary);
        font-size: 12px;

        small {
          color: var(--text-color-secondary);
        }
      }
    }

    .notification-docw {
      position: absolute;
      width: 10px;
      height: 10px;
      background: var(--primary-color);
      right: 8px;
      border-radius: 50%;
      top: 25px;
      box-shadow: 0 0 0 2px var(--card-background), var(--shadow-1);
    }
  }

  :global .notification-unread {
    background: rgba(139, 109, 209, 0.08) !important;
    border-left: 3px solid var(--primary-color) !important;

    .message {
      font-weight: 600;
      color: var(--text-color);
    }

    .sub-message {
      color: var(--text-color-secondary);
      font-weight: 500;
    }

    .time {
      color: var(--text-color-secondary);
    }
  }

  :global .notification-read {
    .message {
      color: var(--text-color-secondary);
      font-weight: normal;
    }

    .sub-message {
      color: var(--text-color-tertiary);
    }

    .time {
      color: var(--text-color-tertiary);
    }
  }
  :global .ant-row .ant-col {
    padding-top: 0;
    padding-bottom: 0;
    &.ant-col-md-3 {
      flex: inherit;
      max-width: none;
      width: 40px;
    }
    &.ant-col-md-18 {
      flex: inherit;
      max-width: none;
      width: calc(100% - 55px);
    }
  }
}
.icon-dismiss {
  position: absolute;
  width: 10px;
  height: 10px;
  background: var(--primary-color);
  right: -5px;
  border-radius: 50%;
  top: 50%;
  margin-top: -5px;
  box-shadow: var(--shadow-1);
}
