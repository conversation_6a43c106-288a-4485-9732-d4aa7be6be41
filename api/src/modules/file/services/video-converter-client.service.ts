import { Injectable } from '@nestjs/common';
import { ConfigService } from 'nestjs-config';
import axios from 'axios';
import * as FormData from 'form-data';
import { createReadStream, existsSync, createWriteStream, statSync } from 'fs';
import { join } from 'path';
import { StringHelper } from 'src/kernel';
import { io, Socket } from 'socket.io-client';
import {
  IConvertOptions,
  IConvertProgress,
  IConvertResponse
} from './video.service';

@Injectable()
export class VideoConverterClientService {
  private readonly videoConverterApiUrl: string;

  private readonly videoConverterSocketUrl: string;

  private socketConnections = new Map<string, Socket>();

  constructor(private readonly config: ConfigService) {
    // Get video converter API URL from config
    this.videoConverterApiUrl = this.config.get('app.videoConverterApiUrl');
    // Derive WebSocket URL from API URL (replace http with ws, https with wss)
    this.videoConverterSocketUrl = this.videoConverterApiUrl
      .replace(/^https/, 'wss')
      .replace(/^http/, 'ws');
  }

  /**
   * Create a WebSocket connection to the video converter API
   * @param tempId Temporary identifier (will be replaced with actual socket.id)
   * @param onProgress Progress callback function
   * @returns Promise that resolves when connection is established
   */
  private createSocketConnection(
    tempId: string,
    onProgress?: (progress: IConvertProgress) => void
  ): Promise<Socket> {
    return new Promise((resolve, reject) => {
      console.log(
        `Attempting to connect to WebSocket: ${this.videoConverterSocketUrl}`
      );
      const socket = io(this.videoConverterSocketUrl, {
        transports: ['websocket', 'polling'],
        timeout: 20000, // Increased timeout for RunPod proxy
        reconnection: false,
        forceNew: true,
        upgrade: true,
        rememberUpgrade: false
      });

      socket.on('connect', () => {
        console.log(`Connected to video converter WebSocket: ${socket.id}`);
        // Store socket using its actual ID
        this.socketConnections.set(socket.id, socket);
        resolve(socket);
      });

      socket.on('connect_error', (error) => {
        console.error('Failed to connect to video converter WebSocket:', error);
        console.error('WebSocket URL:', this.videoConverterSocketUrl);
        reject(error);
      });

      socket.on('video_conversion_progress', (data) => {
        if (onProgress && data.progress) {
          // Scale conversion progress from 40-100% (conversion phase)
          const scaledProgress = this.scaleConversionProgress(data.progress);
          console.log('Scaled conversion progress:', scaledProgress);
          onProgress(scaledProgress);
        }
      });

      socket.on('video_conversion_complete', (data) => {
        console.log('Video conversion completed:', data);
        this.cleanupSocketConnection(socket.id);
      });

      socket.on('video_conversion_error', (data) => {
        console.error('Video conversion error:', data);
        this.cleanupSocketConnection(socket.id);
      });

      socket.on('disconnect', () => {
        console.log(
          `Disconnected from video converter WebSocket: ${socket.id}`
        );
        this.cleanupSocketConnection(socket.id);
      });
    });
  }

  /**
   * Scale conversion progress from 0-100% to 40-100% (conversion phase)
   * @param progress Original progress from video converter
   * @returns Scaled progress for the conversion phase
   */
  private scaleConversionProgress(progress: IConvertProgress): IConvertProgress {
    // Scale from 0-100% to 40-100%
    const scaledPercent = 40 + (progress.percent * 0.6);

    return {
      ...progress,
      percent: Math.min(Math.round(scaledPercent), 100)
    };
  }

  /**
   * Clean up a WebSocket connection
   * @param socketId Socket identifier to clean up
   */
  private cleanupSocketConnection(socketId: string): void {
    const socket = this.socketConnections.get(socketId);
    if (socket) {
      socket.disconnect();
      this.socketConnections.delete(socketId);
    }
  }

  /**
   * Convert video using the video-converter-api service
   * @param filePath Path to the video file to convert
   * @param options Conversion options
   * @returns Conversion result
   */
  public async convert2Mp4(
    filePath: string,
    options = {} as IConvertOptions
  ): Promise<IConvertResponse> {
    let socketId: string | null = null;
    let socket: Socket | null = null;

    try {
      if (!existsSync(filePath)) {
        throw new Error(`File not found: ${filePath}`);
      }
      console.log('convert2MP4 started!');

      // Create WebSocket connection for progress updates
      if (options.onProgress) {
        try {
          socket = await this.createSocketConnection(
            'temp',
            options.onProgress
          );
          socketId = socket.id; // Use the actual socket client ID
          console.log(`Using socket ID for video conversion: ${socketId}`);
        } catch (socketError) {
          console.warn(
            'Failed to establish WebSocket connection, proceeding without progress updates:',
            socketError
          );
        }
      }
      console.log('upload started...');

      // Upload file to video converter API with progress tracking (0-40%)
      const uploadResult = await this.uploadVideoToConverter(filePath, options.onProgress);
      console.log('upload completed!');

      console.log('convert started...');
      // Request conversion with socket ID for progress updates (40-100%)
      const convertResult = await this.requestConversion(
        uploadResult.data.path,
        { ...options, socketId }
      );
      console.log('convert completed!');

      // Download converted file back to main API
      const downloadResult = await this.downloadConvertedVideo(
        convertResult.data.toPath,
        filePath,
        uploadResult.data.path // Pass original uploaded file path for cleanup
      );
      console.log('download completed!');

      // Clean up socket connection
      if (socketId) {
        this.cleanupSocketConnection(socketId);
      }

      return downloadResult;
    } catch (error) {
      // Clean up socket connection on error
      if (socketId) {
        this.cleanupSocketConnection(socketId);
      }
      console.log(`Video conversion failed: ${error.message}`);
      throw new Error(`Video conversion failed: ${error.message}`);
    }
  }

  /**
   * Upload video file to video converter API
   * @param filePath Path to the video file
   * @param onProgress Optional progress callback for upload phase (0-40%)
   * @returns Upload result
   */
  private async uploadVideoToConverter(
    filePath: string,
    onProgress?: (progress: IConvertProgress) => void
  ): Promise<any> {
    const formData = new FormData();
    const fileStream = createReadStream(filePath);
    formData.append('video', fileStream);

    // Get file size for progress calculation
    const fileStats = statSync(filePath);
    const totalSize = fileStats.size;
    let uploadedSize = 0;

    const response = await axios.post(
      `${this.videoConverterApiUrl}/video-converter/upload`,
      formData,
      {
        headers: {
          ...formData.getHeaders()
        },
        timeout: 300000, // 5 minutes timeout for upload
        onUploadProgress: (progressEvent) => {
          if (onProgress && progressEvent.total) {
            uploadedSize = progressEvent.loaded;
            const uploadPercent = (uploadedSize / progressEvent.total) * 100;
            // Scale upload progress to 0-40% range
            const scaledPercent = Math.min(Math.round(uploadPercent * 0.4), 40);

            onProgress({
              percent: scaledPercent,
              currentTime: '00:00:00',
              targetTime: '00:00:00',
              targetSize: `${Math.round(uploadedSize / 1024)}kB`,
              currentFps: 0,
              currentKbps: 0
            });
          }
        }
      }
    );

    // Emit 40% progress when upload completes
    if (onProgress) {
      onProgress({
        percent: 40,
        currentTime: '00:00:00',
        targetTime: '00:00:00',
        targetSize: `${Math.round(totalSize / 1024)}kB`,
        currentFps: 0,
        currentKbps: 0
      });
    }

    return response.data;
  }

  /**
   * Request video conversion from the video converter API
   * @param remotePath Path to the uploaded file on converter API
   * @param options Conversion options with socketId
   * @returns Conversion result
   */
  private async requestConversion(
    remotePath: string,
    options: IConvertOptions & { socketId?: string }
  ): Promise<any> {
    const convertRequest = {
      filePath: remotePath,
      size: options.size,
      socketId: options.socketId // Include socketId for WebSocket progress updates
    };

    const response = await axios.post(
      `${this.videoConverterApiUrl}/video-converter/convert`,
      convertRequest,
      {
        timeout: 1800000 // 30 minutes timeout for conversion
      }
    );

    return response.data;
  }

  /**
   * Download converted video from video converter API
   * @param remoteConvertedPath Path to converted file on converter API
   * @param originalFilePath Original file path to determine local storage location
   * @param remoteOriginalPath Path to original uploaded file on converter API
   * @returns Local file information
   */
  private async downloadConvertedVideo(
    remoteConvertedPath: string,
    originalFilePath: string,
    remoteOriginalPath?: string
  ): Promise<IConvertResponse> {
    // Generate local path for converted file
    const fileName = `${StringHelper.randomString(5)}_${StringHelper.getFileName(originalFilePath, true)}.mp4`;
    const localPath = join(
      StringHelper.getFilePath(originalFilePath),
      fileName
    );

    // Download file from video converter API
    const response = await axios.get(
      `${this.videoConverterApiUrl}/video-converter/files/download`,
      {
        params: { path: remoteConvertedPath },
        responseType: 'stream',
        timeout: 300000 // 5 minutes timeout for download
      }
    );

    // Save to local file system
    const writer = createWriteStream(localPath);
    response.data.pipe(writer);

    return new Promise((resolve, reject) => {
      writer.on('finish', () => {
        // Clean up both original and converted remote files after successful download
        const filesToCleanup = [remoteConvertedPath];
        if (remoteOriginalPath) {
          filesToCleanup.push(remoteOriginalPath);
        }

        this.cleanupRemoteFiles(filesToCleanup).catch(console.error);

        resolve({
          fileName,
          toPath: localPath
        });
      });
      writer.on('error', reject);
    });
  }

  /**
   * Clean up remote files on video converter API
   * @param remotePaths Path or array of paths to files to clean up
   */
  private async cleanupRemoteFiles(
    remotePaths: string | string[]
  ): Promise<void> {
    try {
      const paths = Array.isArray(remotePaths) ? remotePaths : [remotePaths];

      await axios.delete(
        `${this.videoConverterApiUrl}/video-converter/files/cleanup`,
        {
          data: { paths },
          timeout: 30000 // 30 seconds timeout
        }
      );
    } catch (error) {
      const pathsStr = Array.isArray(remotePaths)
        ? remotePaths.join(', ')
        : remotePaths;
      console.warn(
        `Failed to cleanup remote files ${pathsStr}:`,
        error.message
      );
    }
  }

  /**
   * Get video metadata using video converter API
   * @param filePath Path to video file
   * @returns Video metadata
   */
  public async getMetaData(filePath: string): Promise<any> {
    try {
      // Upload file first
      const uploadResult = await this.uploadVideoToConverter(filePath);

      // Get metadata
      const response = await axios.post(
        `${this.videoConverterApiUrl}/video-converter/metadata`,
        { filePath: uploadResult.data.path },
        { timeout: 60000 }
      );

      // Clean up uploaded file
      this.cleanupRemoteFiles(uploadResult.data.path).catch(console.error);

      return response.data.data;
    } catch (error) {
      throw new Error(`Failed to get video metadata: ${error.message}`);
    }
  }

  /**
   * Check if video supports HTML5 playback
   * @param filePath Path to video file
   * @returns True if supports HTML5
   */
  public async isSupportHtml5(filePath: string): Promise<boolean> {
    try {
      const metadata = await this.getMetaData(filePath);
      if (!metadata?.streams?.length) return false;

      const videoStream = metadata.streams.find(
        (s) => s.codec_type === 'video'
      );
      return ['h264', 'vp8'].includes(videoStream?.codec_name);
    } catch (error) {
      console.warn(
        `Failed to check HTML5 support for ${filePath}:`,
        error.message
      );
      return false;
    }
  }
}
