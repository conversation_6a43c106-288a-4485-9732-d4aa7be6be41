// Global base styles
@import './vars.less';
@import './theme.less';

.ant-layout {
  background-color: transparent;
}

.ant-layout-footer {
  padding: 12px 20px;
  background: var(--gradient-primary);
  color: var(--color-white);
  border-top: 1px solid var(--border-color-base);
}

.page-content {
  padding: 0 16px;
}

.content {
  padding-top: 65px;
  min-height: calc(100vh - 200px);
  background-color: var(--bg-white);
  transition: background-color 0.3s ease;
}

.login-content {
  overflow: hidden;
}

.main-container {
  margin: auto;
  max-width: 1200px;
  width: 100%;
  padding: 20px;
  transition: all 0.3s ease;
}

.ant-page-header {
  padding: 20px 0 0 0;
  font-size: 24px;
  font-weight: 700;
  color: var(--text-color);
  text-transform: capitalize;
  transition: color 0.3s ease;
  border-bottom: 1px solid var(--border-color-base);

  // Page header title styling
  .ant-page-header-heading-title {
    color: var(--text-color) !important;
    font-weight: 700;
    transition: color 0.3s ease;
  }

  // Back button styling
  .ant-page-header-back-button {
    color: var(--text-color-secondary) !important;
    border: 1px solid var(--border-color-base);
    border-radius: 8px;
    background: var(--component-background);
    transition: all 0.3s ease;

    &:hover {
      color: var(--primary-color) !important;
      border-color: var(--primary-color);
      background: var(--item-hover-bg);
      transform: translateX(-2px);
    }

    .anticon {
      color: inherit;
    }
  }

  @media screen and (max-width: @mobile-screen) {
    font-size: 18px;
    padding: 12px 0 0 0;

    .ant-page-header-heading-title {
      font-size: 18px;
    }
  }
}

.page-heading {
  margin: 30px 0 20px 0;
  padding-bottom: 0px;
  font-size: 22px;
  font-weight: 600;
  color: var(--text-color);
  font-weight: bold;
  text-transform: capitalize;
  transition: color 0.3s ease;

  @media screen and (max-width: @mobile-screen) {
    font-size: 16px;
    margin: 15px 0 15px 0;

    .box {
      padding: 5px;
    }
  }
}

.signup-grp-btns {
  margin: 20px 0 25px;
  display: flex;
  justify-content: center;
  align-items: center;

  .ant-btn {
    font-weight: bold;
    width: 300px;
    font-size: 16px;
    height: 50px;

    @media screen and (max-width: @mobile-screen) {
      width: 50%;
      font-size: 12px;
    }
  }
}

.text-center {
  text-align: center;
}

.ant-row {
  margin: 0 -5px;

  .ant-col {
    padding: 5px;
  }
}

.ant-pagination {
  margin: 15px 0;
  text-align: center;
}

.table-responsive {
  overflow-x: auto;
}

.account-form {
  .ant-form-item {
    margin-bottom: 5px;

    .ant-input {
      height: 40px;
    }

    textarea.ant-input {
      min-height: 100px;
    }

    .ant-picker {
      min-height: 40px;
    }

    .ant-select-selector {
      border-radius: 5px;
      min-height: 40px;
      font-size: 13px;
      padding: 0 12px;
    }

    .ant-select-selection-item {
      line-height: 3;
      height: auto;
    }

    .ant-form-item-label {
      padding: 0 5px;

      label {
        height: auto;
      }
    }

    .ant-input-affix-wrapper {
      padding: 0 10px;
    }
  }

  .top-profile {
    margin-bottom: 25px !important;
  }
}

.ant-tabs.ant-tabs-top.nav-tabs {
  margin: 15px 0;
  margin-top: 0;

  .ant-tabs-tab {
    font-size: 15px;
    padding: 7px;
    font-weight: 600;
    font-weight: bold;
  }

  .ant-tabs-tab + .ant-tabs-tab {
    margin: 0 0 0 15px;
  }

  .ant-tabs-content-holder {
    padding: 10px 20px 0 20px;
  }
}

.ant-carousel {
  .slick-slide {
    .ant-image {
      width: 100%;
    }

    img {
      width: 100%;
      object-fit: cover;
    }
  }

  .slick-prev {
    top: calc(50% - 15px);
    left: 0;
    background-color: rgba(0, 0, 0, 0.2);
    padding: 15px;
    z-index: 1;

    &:hover,
    &:focus {
      background-color: rgba(0, 0, 0, 0.5);
    }

    &::before {
      background-image: url(/left-arrow.png);
      background-size: 13px;
      display: inline-block;
      width: 15px;
      height: 15px;
      content: '';
      background-repeat: no-repeat;
      position: absolute;
      left: 8px;
      top: 8px;
    }
  }

  .slick-next {
    top: calc(50% - 15px);
    right: 0;
    background-color: rgba(0, 0, 0, 0.2);
    padding: 15px;
    z-index: 1;

    &:hover,
    &:focus {
      background-color: rgba(0, 0, 0, 0.5);
    }

    &::before {
      background-image: url(/right-arrow.png);
      background-size: 13px;
      display: inline-block;
      width: 15px;
      height: 15px;
      content: '';
      background-repeat: no-repeat;
      position: absolute;
      left: 8px;
      top: 8px;
    }
  }
}

.ant-upload-select.ant-upload-select-picture-card {
  border-radius: 50%;
  border: 2px solid var(--border-color-base);
  background-image: url('/placeholder-image.jpg');
  background-repeat: no-repeat;
  background-size: cover;
  background-position: 50%;
  position: relative;
  background-color: var(--component-background);
  transition: all 0.3s ease;

  &:hover {
    border-color: var(--primary-color);
    transform: scale(1.02);
  }

  img {
    margin-bottom: 10px;
    border-radius: 50%;
    margin-bottom: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .anticon {
    font-size: 18px;
    padding: 8px;
    background-color: var(--primary-color);
    color: var(--color-white);
    border-radius: 50%;
    position: absolute;
    bottom: 15px;
    right: -10px;
    opacity: 0.9;
    transition: all 0.2s ease;

    &:hover {
      opacity: 1;
      transform: scale(1.1);
    }

    &.anticon-file-done {
      background-color: var(--success-color);
    }
  }

  .ant-upload-text {
    display: none;
  }
}

// Enhanced upload styling for registration forms
.upload-card-register {
  .ant-upload-select.ant-upload-select-picture-card {
    border-radius: 8px !important;
    border: 2px dashed var(--border-color-base) !important;
    background-color: var(--bg-secondary) !important;
    background-image: none !important;
    width: 100% !important;
    height: 120px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;

    &:hover {
      border-color: var(--primary-color) !important;
      background-color: var(--bg-hover) !important;
    }

    .anticon {
      position: static !important;
      background-color: transparent !important;
      color: var(--text-color-secondary) !important;
      font-size: 32px !important;
      padding: 0 !important;
      border-radius: 0 !important;
      opacity: 1 !important;

      &:hover {
        color: var(--primary-color) !important;
        transform: none !important;
      }
    }
  }
}

.title-form {
  margin-top: 30px;
  font-size: 1.5em;
  font-weight: 800;
}

.ant-btn.primary {
  background-color: @primary-color;
  color: var(--color-white);
  padding: 0.7em 2em;
  font-size: 0.9em;
  font-weight: 600;
  border-radius: 0.5em;
  min-height: 40px;
  height: auto;
}

.ant-btn.secondary {
  background-color: var(--button-secondary-bg);
  color: var(--button-secondary-text);
  padding: 0.7em 2em;
  font-size: 0.9em;
  font-weight: 600;
  border-radius: 0.5em;
  min-height: 40px;
  height: auto;
  border: 1px solid var(--border-color-base);
  transition: all 0.3s ease;

  &:hover {
    background-color: var(--button-secondary-hover-bg);
    color: var(--button-secondary-hover-text);
    border-color: var(--primary-color);
    transform: translateY(-1px);
    box-shadow: var(--shadow-1);
  }

  &:focus {
    background-color: var(--button-secondary-hover-bg);
    color: var(--button-secondary-hover-text);
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-color-light);
  }

  &:disabled {
    background-color: var(--bg-disabled);
    color: var(--text-color-disabled);
    border-color: var(--border-color-base);
    transform: none;
    box-shadow: none;
    cursor: not-allowed;
  }
}

.ant-btn.button-live {
  background-color: @primary-color;
  box-shadow: 0 4px 4px rgba(0, 0, 0, 0.1);
  padding: 0.7em 2em;
  font-size: 0.9em;
  font-weight: 600;
  border-radius: 0.5em;
  min-height: 40px;
  height: auto;
  color: var(--color-white);
  margin-right: 10px;
  margin-left: 10px;
}

.ant-btn.success {
  background-color: @success-color;
  color: var(--color-white);
  -webkit-box-shadow: 0 5px 10px rgba(50, 50, 50, 0.2);
  box-shadow: 0 5px 10px rgba(50, 50, 50, 0.2);
  padding: 0.7em 2em;
  font-size: 0.9em;
  font-weight: 600;
  border-radius: 0.5em;
  min-height: 40px;
  height: auto;
}

.ant-btn.default {
  background-color: @dark-grey;
  color: var(--color-white);
  -webkit-box-shadow: 0 5px 10px rgba(50, 50, 50, 0.2);
  box-shadow: 0 5px 10px rgba(50, 50, 50, 0.2);
  padding: 0.7em 2em;
  font-size: 0.9em;
  font-weight: 600;
  border-radius: 0.5em;
  min-height: 40px;
  height: auto;

  &:hover {
    background-color: @grey-color;
    border: none;
    color: var(--color-white);
  }
}

.ant-btn.secondary-custom {
  border: none;
  color: var(--color-white);
  padding: 0px 8px;
  min-height: 30px;
  height: auto;
  background-color: @secondary-color;
  margin-right: 5px;
}

.ant-btn.info {
  border: none;
  color: var(--color-white);
  padding: 0px 8px;
  height: 30px;
  background-color: @processing-color;
  margin-right: 5px;
}

.ant-btn.danger {
  border: none;
  color: var(--color-white);
  padding: 0px 8px;
  min-height: 30px;
  height: auto;
  background-color: @highlight-color;
  margin-right: 5px;
}

// .ant-picker.ant-picker-range {
//   border: none;
//   padding: 0;
// }

.table-cart table {
  background-color: var(--component-background);
  border-radius: 0.5em;
  overflow: hidden;
  border: 1px solid var(--border-color-base);
  box-shadow: var(--shadow-1);
  transition: all 0.3s ease;
}

.cart-form input {
  height: 40px;
  background: var(--input-background);
  border-color: var(--border-color-base);
  color: var(--text-color);

  &:hover {
    border-color: var(--primary-color);
  }

  &:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-color-light);
  }
}

.cover-upload {
  float: right;
  margin-top: 5px;

  .ant-upload-select.ant-upload-select-picture-card {
    border-radius: 6px;
    border: 1px solid var(--border-color-base);
    background: var(--bg-secondary);
    width: auto;
    height: auto;
    padding: 8px 16px;
    color: var(--text-color);
    transition: all 0.2s ease;

    &:hover {
      background: var(--bg-hover);
      border-color: var(--primary-color);
      color: var(--primary-color);
    }
  }
}

.ant-image-mask {
  .ant-image-mask-info {
    display: none;
  }
}

.ant-image-preview-mask {
  background-color: var(--bg-white);
}

.ant-image-preview-operations {
  background-color: @grey-color;
}

.middle-split {
  background-color: @light-grey;
  padding: 20px 5px;

  .middle-actions {
    display: flex;
    width: 100%;
    justify-content: space-between;
    align-items: center;

    .o-w-ner {
      display: flex;
      align-items: center;
      font-weight: 600;

      .ant-avatar {
        width: 50px;
        height: 50px;
        object-fit: cover;
      }

      .owner-name {
        padding-left: 5px;
        display: flex;
        flex-direction: column;
      }
    }

    .act-btns {
      .react-btn {
        color: @dark-grey;
        margin: 5px;
        border-radius: 3px;

        &.active {
          color: var(--color-white);
          background: @primary-color;
        }
      }
    }
  }

  @media screen and (max-width: @mobile-screen) {
    .middle-actions {
      flex-direction: column;
    }
  }
}

.streaming-status {
  height: 22px;
  width: 22px;
  border-radius: 50%;
  position: absolute;
  bottom: 10%;
  left: 100px;

  @media (max-width: @tablet-screen) {
    height: 15px;
    width: 15px;
    left: 60px;
  }
}

.private {
  background-color: @private-status-color;
}

.public {
  background-color: @public-status-color;
}

.group {
  background-color: @group-status-color;
}

.offline {
  background-color: @offline-status-color;
}

.middle-info {
  // background-color: @light-blue;
  padding: 20px 5px;

  .vid-tags {
    margin-bottom: 5px;
    display: flex;
    align-items: center;
    flex-wrap: wrap;

    a {
      margin: 2px 3px;
    }

    .ant-tag {
      color: @primary-color;
    }
  }

  .ant-tabs-nav {
    border-top: 1px solid #ddd;
    border-bottom: 1px solid #ddd;

    &::before {
      display: none;
    }
  }

  .ant-tabs {
    overflow: unset;
  }

  .ant-tabs-tab {
    font-weight: 600;
    font-size: 16px;
    color: @black;
    padding: 10px 15px;
    font-weight: bold;

    @media screen and (max-width: @mobile-screen) {
      font-size: 13px;
    }
  }

  .ant-tabs-tab-active {
    color: @primary-color;
  }
}

.related-items {
  margin: 30px 0;

  .ttl-1 {
    font-size: 1.5em;
    font-weight: 800;
    font-weight: bold;
  }
}

.videojs-player {
  .video-js {
    max-height: 100%;
    width: 100%;

    .vjs-big-play-button {
      top: calc(50% - 25px);
      left: calc(50% - 45px);
    }

    .vjs-picture-in-picture-control {
      display: none;
    }
  }
}

.ant-popover {
  .ant-popover-inner-content {
    padding: 0;
  }
}

.verify-info {
  padding: 15px;
  border-radius: 5px;
  text-align: center;
  color: var(--color-white);
  background-color: var(--error-color);
  margin-bottom: 10px;

  p {
    margin: 0;
  }
}

.ant-result {
  .ant-result-subtitle {
    font-size: 20px;
  }
}

// Enhanced Result component styling for better UX
.ant-result {
  padding: 48px 24px !important;
  text-align: center !important;

  .ant-result-icon {
    margin-bottom: 32px !important;

    // Enhanced icon sizes for better visibility
    .anticon {
      font-size: 72px !important;
    }
  }

  .ant-result-title {
    font-size: 24px !important;
    font-weight: 600 !important;
    margin-bottom: 16px !important;
    line-height: 1.4 !important;
  }

  .ant-result-subtitle {
    font-size: 16px !important;
    line-height: 1.6 !important;
    margin-bottom: 32px !important;
    max-width: 600px !important;
    margin-left: auto !important;
    margin-right: auto !important;
  }

  .ant-result-extra {
    margin-top: 40px !important;

    .ant-btn {
      margin: 0 8px 16px 8px !important;
      min-width: 140px !important;
      height: 44px !important;
      font-size: 14px !important;
      font-weight: 500 !important;
      border-radius: 8px !important;

      @media (max-width: 576px) {
        display: block !important;
        width: 100% !important;
        margin: 8px 0 !important;
      }
    }
  }

  // Responsive adjustments
  @media (max-width: 768px) {
    padding: 32px 16px !important;

    .ant-result-icon .anticon {
      font-size: 56px !important;
    }

    .ant-result-title {
      font-size: 20px !important;
    }

    .ant-result-subtitle {
      font-size: 14px !important;
    }
  }
}

#publisher {
  min-height: 501px;
  width: 100%;
  object-fit: cover;

  @media (max-width: @mobile-screen) {
    min-height: auto;
    height: 100vh;
    width: 100%;
  }
}

#subscriber {
  min-height: 500px;
  width: 100%;

  video {
    object-fit: cover;
  }

  @media (max-width: @mobile-screen) {
    height: 100vh;
  }

  // video {
  //   height: 480px;
  //   width: 100%;
  // }
}

// #subscriber.vjs-fullscreen {
//   video {
//     height: 100% !important;
//   }
// }

.private-streaming-container {
  position: absolute;
  height: 500px;
  width: 100%;
  background-color: #333;
  top: 0;
  left: 0;

  #private-publisher {
    right: 10px;
    top: 57px;
    height: 120px;
    width: 95px;
    position: absolute;
    z-index: 1;
    border-radius: 5px;
    object-fit: cover;

    video {
      height: 100% !important;
      width: 100% !important;
      object-fit: cover;
    }
  }

  #private-subscriber {
    width: 100% !important;
    // height: 100% !important;
    // min-height: 520px;
    // object-fit: cover;
    height: 100%;
    padding-top: 500px;

    video {
      height: 100% !important;
      width: 100% !important;
      object-fit: cover;
    }
  }

  @media screen and (max-width: @mobile-screen) {
    min-height: 520px;
    height: auto;

    #private-subscriber {
      height: 100vh;
      min-height: auto;
      padding: 0;

      video {
        height: 100% !important;
        width: 100% !important;
        object-fit: cover;
      }
    }

    .vjs-big-play-button {
      display: none;
    }

    #private-publisher {
      right: 10px;
      top: 60px;
      height: 120px;
      width: 95px;

      video {
        height: 100% !important;
        width: 100% !important;
        object-fit: cover;
      }
    }
  }
}

.ant-menu-light .ant-menu-item:hover,
.ant-menu-light .ant-menu-item-active,
.ant-menu-light .ant-menu:not(.ant-menu-inline) .ant-menu-submenu-open,
.ant-menu-light .ant-menu-submenu-active,
.ant-menu-light .ant-menu-submenu-title:hover {
  .message {
    color: @primary-color;
  }

  .time {
    color: @primary-color;
  }
}

.notification-item-list:hover {
  .message {
    color: @primary-color;
  }

  .time {
    color: @primary-color;
  }
}

.img-cover {
  background-position: center center;
  background-size: cover;
  background-repeat: no-repeat;
  padding: 36% 0;
  display: block;

  img {
    opacity: 0;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
}

.sun-editor-editable * {
  font-family: 'Poppins', sans-serif !important;
}

.ant-dropdown {
  box-shadow: 0 0 10px 1px rgba(0, 0, 0, 0.2);
  border-radius: 5px;
}

.box-panel,
.collapse-custom {
  border-radius: 5px;
  margin-bottom: 20px;
  background-color: var(--bg-panel-white);

  .title-box-panel {
    border-bottom: 1px solid var(--light-grey-border);

    h3 {
      font-size: 16px;
      padding: 15px;
      font-weight: bold;
      margin: 0;
    }
  }

  .ant-collapse-header {
    font-size: 16px;
    padding: 15px;
    font-weight: bold;
  }

  .body-box-panel,
  .ant-collapse-content-box {
    padding: 15px !important;
    color: var(--text-color) !important;
  }
}

.no-item {
  padding: 20px;
}

.ant-modal {
  .ant-modal-footer {
    .ant-btn {
      margin-left: 5px !important;
    }
  }

  .ant-modal-title {
    font-weight: bold;
    font-size: 18px;
  }

  .ant-modal-close {
    top: 7px;
    right: 7px;

    .ant-modal-close-x {
      width: 40px;
      height: 40px;
      line-height: 40px;
      text-align: center;
      border-radius: 50%;
      transition: all 0.3s;
      background-color: var(--bg-white);
      color: #7b5dbd;

      &:hover {
        background-color: var(--item-hover-bg);
      }
    }
  }
}

.ant-tabs-dropdown-menu-item {
  div {
    display: inline-block;
    vertical-align: middle;
    margin-left: 5px;
  }
}

.feedpage {
  max-width: 900px;

  .main-background {
    margin: auto;
    border: 1px solid var(--light-grey-border);
  }
}

.ant-table {
  // border: 1px solid #ddd;
  .ant-table-thead > tr > th {
    border-color: var(--light-grey-border);
    border-top: 1px solid var(--light-grey-border);

    &:first-child {
      border-left: 1px solid var(--light-grey-border);
    }

    &:last-child {
      border-right: 1px solid var(--light-grey-border);
    }
  }

  .ant-table-tbody > tr > td {
    border-color: var(--light-grey-border);
    background: var(--bg-white);
    padding: 10px;

    &:first-child {
      border-left: 1px solid var(--light-grey-border);
    }

    &:last-child {
      border-right: 1px solid var(--light-grey-border);
    }

    span {
      display: inline-block;
      max-width: 150px;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}

.ant-page-header-heading {
  margin-bottom: 10px;
}

.show-desktop {
  @media screen and (max-width: @mobile-screen) {
    display: none !important;
  }
}

.show-mobile {
  display: none !important;

  @media screen and (max-width: @mobile-screen) {
    display: block !important;
  }
}

.ant-table-pagination-right {
  justify-content: center;
}

.color-primary {
  color: @primary-color;
}

.list-performer {
  margin: 0 -10px;

  @media (max-width: @mobile-screen) {
    margin: 0 -5px;
  }

  .ant-col {
    padding: 10px;

    @media (max-width: @mobile-screen) {
      padding: 5px;
    }
  }
}

.ant-select-dropdown {
  box-shadow: 0 0 10px 1px rgba(0, 0, 0, 0.2);
  padding: 0;
}

.ant-modal-body {
  .videojs-player {
    padding: 0 !important;

    .video-js {
      padding-top: 0 !important;
      max-height: calc(100vh - 100px) !important;
      background-color: var(--component-background);
    }
  }
}

.modal-custom {
  .ant-modal-body {
    padding: 0;
  }

  .modal-body-custom {
    padding: 24px;
  }

  .modal-button-custom {
    padding: 15px 24px;
    border-top: 1px solid var(--border-color-base);
    text-align: right;
    background: var(--bg-modal);

    .ant-btn {
      margin-left: 5px;

      &.primary {
        background: var(--gradient-primary) !important;
        border: none !important;
        color: var(--color-white) !important;
        font-weight: 600;
        height: 40px;
        border-radius: 8px;
        transition: all 0.3s ease;
        box-shadow: var(--shadow-1);

        &:hover {
          transform: translateY(-1px);
          box-shadow: var(--shadow-2);
        }

        &:focus {
          box-shadow: 0 0 0 2px var(--primary-color-light);
        }

        &:disabled,
        &[loading] {
          background: var(--bg-disabled) !important;
          color: var(--text-color-disabled) !important;
          transform: none;
          box-shadow: none;
          cursor: not-allowed;
        }
      }

      &.secondary {
        background: var(--button-secondary-bg) !important;
        color: var(--button-secondary-text) !important;
        border: 1px solid var(--border-color-base) !important;
        font-weight: 600;
        height: 40px;
        border-radius: 8px;
        transition: all 0.3s ease;

        &:hover {
          background: var(--button-secondary-hover-bg) !important;
          color: var(--button-secondary-hover-text) !important;
          border-color: var(--primary-color) !important;
          transform: translateY(-1px);
          box-shadow: var(--shadow-1);
        }

        &:focus {
          background: var(--button-secondary-hover-bg) !important;
          color: var(--button-secondary-hover-text) !important;
          border-color: var(--primary-color) !important;
          box-shadow: 0 0 0 2px var(--primary-color-light);
        }

        &:disabled {
          background: var(--bg-disabled) !important;
          color: var(--text-color-disabled) !important;
          border-color: var(--border-color-base) !important;
          transform: none;
          box-shadow: none;
          cursor: not-allowed;
        }
      }
    }
  }
}

.modal-bottom {
  @media (max-width: @mobile-screen) {
    bottom: 0 !important;
    top: auto !important;
    transform: none !important;
    position: absolute;
    left: 0;
    margin: 0;
    padding-bottom: 0;
    max-width: 100%;

    .ant-modal-content {
      background-color: #232323;
      border-radius: 10px 10px 0 0;
      color: #fff;

      .ant-modal-header {
        background-color: transparent;
        color: #fff;
        border: 0;

        .ant-modal-title {
          color: #fff;
          text-align: center;
        }
      }
    }

    .ant-modal-close .ant-modal-close-x {
      background-color: transparent;
      color: #999;
    }

    .ant-modal-body {
      padding: 0 10px 10px 10px;

      .content-body-creator {
        background-color: #333;
        border-radius: 5px;
        padding: 15px;
        max-height: calc(100vh - 180px);
        overflow: auto;
      }

      .ant-input-number {
        background-color: transparent;
        border-color: #666;
        color: #fff;
      }
    }

    .ant-modal-footer {
      text-align: center;
      border: 0;
    }
  }
}

.selected-list {
  background-color: var(--bg-secondary);
  pointer-events: none;

  &:after {
    content: '';
    display: block;
    box-sizing: border-box;
    position: absolute;
    right: 15px;
    top: 10px;
    width: 6px;
    height: 10px;
    border-width: 0 2px 2px 0;
    border-style: solid;
    transform-origin: bottom left;
    transform: rotate(45deg);
    border-color: green;
  }
}

.header-page {
  display: flex;
  align-items: center;

  .header-page-right {
    margin-left: auto;
    display: flex;

    @media screen and (max-width: @tablet-screen) {
      .ant-btn {
        padding: 5px;
      }
    }
  }
}

.button-bottom-form {
  margin: 20px 0;
  text-align: center;

  .ant-btn {
    margin-left: 5px;
  }
}

.main-container-900 {
  width: 900px;
  max-width: 100%;
  margin: auto;
  padding: 0 15px;
}

body.ant-scrolling-effect {
  overflow: hidden;
  touch-action: none;
  -ms-touch-action: none;
  position: relative;
  height: 100%;
}

.coupon-form {
  display: flex;

  input {
    width: calc(100% - 110px);
    height: 40px;
  }

  .ant-btn.default {
    width: 100px;
    padding: 5px 10px;
    margin-left: auto;
    border-radius: 3px;
  }
}

.page-streaming {
  @media screen and (max-width: @mobile-screen) {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 999;
    background: #000;
  }

  .show-streaming {
    position: relative;
    min-height: 490px;
    background-color: #333;

    @media screen and (max-width: @mobile-screen) {
      position: absolute;
      top: 0;
      left: 0;
      width: 100% !important;
      height: 100% !important;
      min-height: auto;
    }

    &-box {
      position: relative;
      overflow: hidden;
      height: 500px;

      @media screen and (max-width: @mobile-screen) {
        height: 100vh !important;
      }
    }

    &-bg {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 1;
      background-size: cover;
      background-repeat: no-repeat;
      background-position: center center;
      filter: blur(9px);
      transform: scale(1.05);

      &::before {
        content: ' ';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 2;
      }
    }

    &-center {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      z-index: 3;
      color: #ffff;

      @media screen and (max-width: @mobile-screen) {
        top: 45%;
      }

      .box-streaming-center {
        background-color: rgba(0, 0, 0, 0.2);
        border-radius: 10px;
        overflow: hidden;
        width: 280px;
        max-width: 90%;
        text-align: center;
        margin: auto;

        .streaming-title {
          background-color: var(--primary-color);
          padding: 10px;
          font-size: 16px;
          font-weight: bold;
        }

        .streaming-content {
          padding: 15px;
          display: inline-block;
          font-size: 15px;
          white-space: nowrap;

          h4 {
            color: #fff;
          }

          img {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-bottom: 5px;
            display: inline-block;
          }

          .ant-btn {
            margin-top: 10px;
          }
        }
      }
    }
  }

  .buttons-stream-mobile {
    position: absolute;
    bottom: 52px;
    right: 10px;
    z-index: 5;
    display: none;

    @media screen and (max-width: @mobile-screen) {
      display: block;
    }

    > div,
    button {
      width: 40px;
      height: 40px;
      line-height: 45px;
      border-radius: 50%;
      background-color: rgba(0, 0, 0, 0.5);
      text-align: center;
      display: block;
      margin: 5px 0;
      position: relative;
      padding: 0;
      border: 0;
      color: #fff;

      .number-badge {
        position: absolute;
        top: -3px;
        right: -3px;
        width: 18px;
        height: 18px;
        text-align: center;
        background-color: red;
        border-radius: 50%;
        color: #fff;
        font-weight: bold;
        line-height: 19px;
      }

      .anticon {
        font-size: 19px;
      }
    }
  }

  .show-streaming-button {
    padding: 10px;
    background-color: var(--bg-secondary);

    .close-streaming {
      .anticon {
        display: none;
      }
    }

    @media screen and (max-width: @mobile-screen) {
      position: absolute;
      top: 61%;
      left: 50%;
      transform: translate(-50%, 0);
      z-index: 10;
      background-color: transparent;

      .close-streaming {
        display: none;
      }
    }

    &.stop-streaming {
      @media screen and (max-width: @mobile-screen) {
        top: 5px;
        left: 5px;
        transform: none;
        display: none;
      }
    }

    &.join-streaming {
      @media screen and (max-width: @mobile-screen) {
        transform: none;
        top: 10px;
        right: 10px;
        z-index: 10;
        left: auto;
        padding: 0;
      }

      .close-streaming {
        @media screen and (max-width: @mobile-screen) {
          color: #fff;
          width: 35px;
          height: 35px;
          line-height: 35px;
          text-align: center;
          background-color: var(--primary-color);
          font-size: 18px;
          display: block;
          border-radius: 20px;
          padding: 0;
          border: 0;
          display: block;

          .hide {
            display: none;
          }

          .anticon {
            display: block;
            margin: 0;
          }
        }
      }
    }
  }

  .requesting-private {
    position: absolute;
    top: calc(50% - 100px);
    left: 50%;
    transform: translate(-50%, 50%);
    z-index: 10;
    background-color: transparent;
    color: #fff;
    font-size: 16px;
    width: 95%;
    text-align: center;

    .requesting-private-load {
      position: relative;
      width: 50px;
      height: 50px;
      margin: auto;
      margin-bottom: 10px;

      &::before,
      &::after {
        content: ' ';
        width: 100%;
        height: 100%;
        border-radius: 50%;
        background-color: rgba(255, 255, 255, 0.3);
        position: absolute;
        top: 0;
        left: 0;
        animation: calling 2s ease-in-out infinite;
      }

      &::before {
        animation-delay: 0.1s;
      }

      &::after {
        animation-delay: 0.5s;
      }

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 50%;
        position: relative;
        z-index: 5;
      }
    }
  }

  .hide-chat {
    border-radius: 20px;
    background: rgba(0, 0, 0, 0.5);
    color: #fff;
    display: inline-block;
    padding: 3px 10px;
    margin-bottom: 5px;
    margin-left: 7px;

    @media screen and (max-width: @mobile-screen) {
      display: inline-block !important;
    }
  }

  .ant-col {
    position: static;
    padding: 0 5px;
  }

  .left-top-streaming {
    position: absolute;
    top: 10px;
    left: 10px;
    z-index: 10;
    font-size: 13px;
    width: calc(100% - 62px);

    > div {
      display: inline-block;
      color: #fff;
      border-radius: 20px;
      margin: 0 5px 3px 0;
      background: rgba(0, 0, 0, 0.5);
      padding: 3px 8px;

      @media screen and (max-width: @mobile-screen) {
        padding: 3px 5px;
      }

      .anticon {
        margin-right: 3px;
      }
    }

    .page-heading {
      position: relative;
      padding: 3px 10px 3px 3px;
      font-size: 13px;

      .ant-avatar {
        width: 25px;
        height: 25px;
      }
    }
  }

  .right-top-streaming {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 10;
    text-align: right;

    > div {
      display: inline-block;
      color: #fff;
      border-radius: 20px;
      margin: 0 0 3px 5px;
      background: rgba(0, 0, 0, 0.2);
      padding: 5px 10px;
      font-size: 13px;
    }

    .close-streaming {
      color: #fff;
      width: 35px;
      height: 35px;
      line-height: 35px;
      text-align: center;
      background-color: var(--primary-color);
      font-size: 18px;
      display: block;
      border-radius: 20px;
      padding: 0;
      border: 0;
      display: inline-block;
    }
  }

  .icon-streaming {
    display: block;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: var(--primary-color);
    text-align: center;
    margin: 0 auto 10px auto;
    color: #fff;
    padding-top: 10px;

    svg {
      width: 35px;
      height: 35px;
      display: inline-block;
    }
  }

  @media screen and (max-width: @mobile-screen) {
    .vjs-control-bar {
      display: none !important;
    }
  }
}

.page-streaming-creator {
  .show-streaming {
    .show-streaming-bg {
      height: 501px;
    }

    .show-streaming-box {
      &::before {
        display: none;
      }
    }
  }

  .privateRequestTotal-btn {
    position: relative;

    .privateRequestTotal {
      position: absolute;
      top: -3px;
      right: -3px;
      width: 18px;
      height: 18px;
      text-align: center;
      background-color: var(--error-color);
      border-radius: 50%;
      color: #fff;
      font-weight: bold;
      line-height: 19px;
    }
  }
}

.loader {
  display: inline-block;
}

/* Creating the dots */
.loader span {
  height: 3px;
  width: 3px;
  margin-right: 3px;
  border-radius: 50%;
  background-color: #fff;
  animation: loading 1s linear infinite;
  display: inline-block;
  vertical-align: middle;
}

.loader span:nth-child(1) {
  animation-delay: 0.1s;
}

.loader span:nth-child(2) {
  animation-delay: 0.2s;
}

.loader span:nth-child(3) {
  animation-delay: 0.3s;
}

@keyframes loading {
  0% {
    transform: translateY(0);
  }

  25% {
    transform: translateY(3px);
  }

  50% {
    transform: translateY(-3px);
  }

  100% {
    transform: translateY(0);
  }
}

@keyframes calling {
  0% {
    transform: scale(1);
    opacity: 0.5;
  }

  100% {
    transform: scale(1.5);
    opacity: 0;
  }
}

.item-description {
  margin-bottom: 5px;
  position: relative;
  font-size: 13px;

  :last-child {
    margin: 0;
  }

  .title-item-description {
    display: inline;
    font-weight: bold;
  }

  .txt-item-description {
    display: inline;
    text-transform: capitalize;

    .ant-tag {
      margin-bottom: 3px;
    }
  }
}

.ant-modal-mask {
  backdrop-filter: blur(8px);
}

// Promotion form styling - Professional, modern, smart and intuitive UI
.promotion-form-card {
  max-width: 600px;
  margin: 0 auto;
  box-shadow: var(--shadow-1);
  border-radius: 12px;
  border: 1px solid var(--border-color-base);
  background: var(--card-background);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;

  &:hover {
    box-shadow: var(--shadow-2);
    transform: translateY(-2px);
  }

  .ant-card {
    background: transparent;
    border: none;
    box-shadow: none;
  }

  .ant-card-body {
    padding: 32px;
    background: transparent;

    @media (max-width: 768px) {
      padding: 24px 20px;
    }
  }

  .ant-form-item {
    margin-bottom: 28px;

    .ant-form-item-label {
      margin-bottom: 12px;

      label {
        color: var(--text-color);
        font-weight: 600;
        font-size: 15px;
        display: flex;
        align-items: center;
        gap: 8px;

        .anticon {
          color: var(--primary-color);
          font-size: 16px;
        }
      }
    }

    .ant-form-item-extra {
      color: var(--text-color-secondary);
      font-size: 13px;
      margin-top: 8px;
      padding: 8px 12px;
      background: var(--bg-secondary);
      border-radius: 6px;
      border-left: 3px solid var(--primary-color);
    }
  }

  .ant-input-number {
    width: 100%;
    border-radius: 8px;
    border: 2px solid var(--border-color-base);
    background: var(--input-background);
    transition: all 0.3s ease;

    &:hover {
      border-color: var(--primary-color);
      box-shadow: 0 0 0 2px var(--primary-color-light);
    }

    &:focus-within {
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px var(--primary-color-light);
    }
  }

  .ant-radio-group {
    width: 100%;
    display: flex;
    gap: 12px;
    padding: 4px;
    background: var(--bg-secondary);
    border-radius: 10px;
    border: 1px solid var(--border-color-base);

    @media (max-width: 480px) {
      flex-direction: column;
      gap: 8px;
    }
  }

  .ant-radio-button-wrapper {
    flex: 1;
    text-align: center;
    border: none !important;
    border-radius: 8px !important;
    background: transparent;
    color: var(--text-color-secondary);
    font-weight: 500;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;

    &::before {
      display: none;
    }

    &:hover {
      background: var(--item-hover-bg);
      color: var(--text-color);
      transform: translateY(-1px);
    }

    &.ant-radio-button-wrapper-checked {
      background: var(--primary-color) !important;
      color: var(--color-white) !important;
      box-shadow: var(--shadow-1);
      transform: translateY(-1px);

      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
          135deg,
          transparent 0%,
          rgba(255, 255, 255, 0.1) 100%
        );
        pointer-events: none;
      }
    }
  }

  .ant-select {
    .ant-select-selector {
      border-radius: 8px;
      border: 2px solid var(--border-color-base);
      background: var(--input-background);
      transition: all 0.3s ease;
      padding: 8px 16px;
      min-height: 48px;

      .ant-select-selection-item {
        color: var(--text-color);
        font-weight: 500;
        line-height: 32px;
      }

      .ant-select-selection-placeholder {
        color: var(--text-color-tertiary);
        line-height: 32px;
      }
    }

    &:hover .ant-select-selector {
      border-color: var(--primary-color);
      box-shadow: 0 0 0 2px var(--primary-color-light);
    }

    &.ant-select-focused .ant-select-selector {
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px var(--primary-color-light);
    }
  }

  .ant-btn {
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;

    &.ant-btn-primary {
      background: var(--gradient-primary);
      border: none;
      color: var(--color-white);
      box-shadow: var(--shadow-1);

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
          135deg,
          transparent 0%,
          rgba(255, 255, 255, 0.1) 100%
        );
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      &:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-2);

        &::before {
          opacity: 1;
        }
      }

      &:active {
        transform: translateY(0);
      }
    }
  }
}

.promotion-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--border-color-split);

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
}

.promotion-stats-badge {
  background: var(--bg-secondary);
  padding: 12px 20px;
  border-radius: 20px;
  border: 1px solid var(--border-color-base);
  color: var(--text-color);
  font-weight: 600;
  font-size: 14px;
  box-shadow: var(--shadow-1);
  transition: all 0.3s ease;

  &:hover {
    background: var(--item-hover-bg);
    transform: translateY(-1px);
    box-shadow: var(--shadow-2);
  }

  @media (max-width: 768px) {
    text-align: center;
    padding: 10px 16px;
    font-size: 13px;
  }
}

.promotion-stats {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-top: 16px;
}

.stat-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 16px;
  background: var(--bg-secondary);
  border-radius: 12px;
  border: 1px solid var(--border-color-base);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-secondary);
    opacity: 0.05;
    pointer-events: none;
  }

  &:hover {
    background: var(--item-hover-bg);
    transform: translateY(-2px);
    box-shadow: var(--shadow-1);
  }

  @media (max-width: 768px) {
    padding: 14px;
    gap: 12px;
  }
}

.stat-icon {
  font-size: 22px;
  padding: 12px;
  border-radius: 12px;
  transition: all 0.3s ease;
  box-shadow: var(--shadow-1);

  @media (max-width: 768px) {
    font-size: 20px;
    padding: 10px;
  }

  &.success {
    color: var(--success-color);
    background: var(--success-color);
    background: rgba(16, 209, 58, 0.1);
    border: 1px solid rgba(16, 209, 58, 0.2);
  }

  &.primary {
    color: var(--primary-color);
    background: var(--primary-color-light);
    border: 1px solid var(--primary-color-fade);
  }
}

.stat-content {
  display: flex;
  flex-direction: column;
  gap: 6px;
  flex: 1;

  .ant-typography {
    margin: 0;

    &.ant-typography-strong {
      color: var(--text-color);
      font-size: 15px;
      font-weight: 600;
    }

    &[type='secondary'] {
      color: var(--text-color-secondary);
      font-size: 13px;
      line-height: 1.5;
    }
  }
}

.custom-table {
  border-radius: 8px;
  overflow: hidden;
}

.custom-table .ant-table-thead > tr > th {
  background: #fafafa;
  font-weight: 600;
}

.custom-table .ant-table-tbody > tr:hover > td {
  background: #f5f7ff;
}

.media-preview-stack {
  position: relative;
  perspective: 1000px;
}

.media-preview-item {
  transition: transform 0.3s ease;
}

.media-preview-item:hover {
  transform: scale(1.05);
  z-index: 10;
}

@media (max-width: 768px) {
  .header-page {
    padding: 16px;
  }

  .ant-card-body {
    padding: 16px;
  }

  .ant-table {
    font-size: 14px;
  }
}

// Dark mode specific overrides
.dark-mode {
  .ant-picker-dropdown {
    .ant-picker-cell-in-view {
      // Selected date background
      &.ant-picker-cell-selected .ant-picker-cell-inner,
      &.ant-picker-cell-range-start .ant-picker-cell-inner,
      &.ant-picker-cell-range-end .ant-picker-cell-inner {
        color: #fff;
        background: @primary-color;
      }

      // Range selection background
      &.ant-picker-cell-in-range::before {
        background: fade(@primary-color, 15%);
      }

      // Range start/end background
      &.ant-picker-cell-range-start:not(
          .ant-picker-cell-range-start-single
        )::before,
      &.ant-picker-cell-range-end:not(
          .ant-picker-cell-range-end-single
        )::before {
        background: fade(@primary-color, 15%);
      }
    }
  }

  // Custom table dark mode fixes
  .custom-table .ant-table-thead > tr > th {
    background: var(--bg-secondary) !important;
    color: var(--text-color) !important;
  }

  .custom-table .ant-table-tbody > tr:hover > td {
    background: var(--item-hover-bg) !important;
  }

  // Stat content dark mode
  .stat-content {
    color: var(--text-color);
  }

  // Media preview dark mode
  .media-preview-item {
    background: var(--component-background);
    border-color: var(--border-color-base);
  }

  // Contact page specific dark mode fixes
  .contact-content {
    background: var(--bg-card) !important;
    color: var(--text-color) !important;
    border-color: var(--border-color-base) !important;
  }

  // Result component dark mode specific overrides
  .ant-result {
    background: transparent !important;
    color: var(--text-color) !important;

    .ant-result-title {
      color: var(--text-color) !important;
    }

    .ant-result-subtitle {
      color: var(--text-color-secondary) !important;
    }

    // Enhanced icon colors for dark mode
    .ant-result-icon {
      .anticon {
        // Error status - enhanced red for dark mode
        &.anticon-close-circle {
          color: #ff5252 !important;
        }

        // Success status - enhanced green for dark mode
        &.anticon-check-circle {
          color: #10d13a !important;
        }

        // Warning status - enhanced yellow for dark mode
        &.anticon-exclamation-circle {
          color: #ffc107 !important;
        }

        // Info status - enhanced blue for dark mode
        &.anticon-info-circle {
          color: var(--info-color) !important;
        }

        // 404 status - muted color for dark mode
        &.anticon-file-search {
          color: var(--text-color-tertiary) !important;
        }
      }
    }

    .ant-result-extra {
      .ant-btn {
        &.primary {
          background: var(--primary-gradient) !important;
          border: none !important;
          color: var(--color-white) !important;

          &:hover {
            background: var(--primary-gradient-hover) !important;
            box-shadow: var(--shadow-2) !important;
          }
        }

        &.secondary {
          background: var(--bg-card) !important;
          border-color: var(--border-color-base) !important;
          color: var(--text-color) !important;

          &:hover {
            background: var(--bg-hover) !important;
            border-color: var(--primary-color) !important;
            color: var(--primary-color) !important;
          }
        }
      }
    }
  }

  // Global component dark mode fixes
  .dropdown-content {
    background-color: var(--component-background) !important;
    border-color: var(--border-color-base) !important;
    box-shadow: var(--shadow-2) !important;

    a {
      color: var(--text-color) !important;

      &:hover {
        background-color: var(--bg-hover) !important;
        color: var(--primary-color) !important;
      }
    }
  }

  // Modal and popup dark mode fixes
  .ant-modal {
    .ant-modal-content {
      background: var(--bg-modal) !important;
      color: var(--text-color) !important;
    }

    .ant-modal-header {
      background: var(--bg-modal) !important;
      border-color: var(--border-color-base) !important;

      .ant-modal-title {
        color: var(--text-color) !important;
      }
    }

    .ant-modal-body {
      background: var(--bg-modal) !important;
      color: var(--text-color) !important;
    }

    .ant-modal-footer {
      background: var(--bg-modal) !important;
      border-color: var(--border-color-base) !important;
    }
  }

  // Category modal specific fixes
  .add-category {
    background: var(--bg-secondary) !important;
    border-color: var(--border-color-base) !important;

    &:hover {
      background: var(--bg-hover) !important;
      border-color: var(--primary-color) !important;
    }
  }

  .category-list {
    background: var(--component-background) !important;
  }

  .category-tag {
    background: var(--component-background) !important;
    border-color: var(--border-color-split) !important;

    &:hover {
      border-color: var(--border-color-base) !important;
    }
  }

  .category-name {
    color: var(--text-color) !important;
  }

  .subscriber-count {
    color: var(--text-color-secondary) !important;
    background: var(--bg-secondary) !important;
  }

  // Store page specific dark mode fixes
  .middle-split {
    background-color: var(--bg-secondary) !important;

    .middle-actions {
      .o-w-ner {
        .owner-name {
          color: var(--text-color) !important;

          span {
            color: var(--text-color) !important;

            &:last-child {
              color: var(--text-color-secondary) !important;
            }
          }
        }
      }

      .act-btns {
        .react-btn {
          color: var(--text-color-secondary) !important;
          background: var(--bg-card) !important;
          border-color: var(--border-color-base) !important;

          &:hover {
            color: var(--primary-color) !important;
            background: var(--bg-hover) !important;
            border-color: var(--primary-color) !important;
          }

          &.active {
            color: var(--primary-color) !important;
            background: var(--primary-color-fade) !important;
            border-color: var(--primary-color) !important;
          }
        }
      }
    }
  }

  .related-items {
    .ttl-1 {
      color: var(--text-color) !important;
    }
  }

  // Product card dark mode fixes
  .product-card {
    .prod-info {
      .prod-desc {
        color: var(--text-color-secondary) !important;
      }

      .add-cart {
        .ant-btn {
          &.primary {
            background: var(--primary-color) !important;
            border-color: var(--primary-color) !important;
            color: var(--color-white) !important;

            &:hover {
              background: var(--primary-color) !important;
              border-color: var(--primary-color) !important;
              opacity: 0.8;
            }

            &:disabled {
              background: var(--bg-disabled) !important;
              border-color: var(--border-color-base) !important;
              color: var(--text-color-disabled) !important;
            }
          }

          &.secondary {
            background: var(--bg-card) !important;
            border-color: var(--border-color-base) !important;
            color: var(--text-color) !important;

            &:hover {
              background: var(--bg-hover) !important;
              border-color: var(--primary-color) !important;
              color: var(--primary-color) !important;
            }

            &:disabled {
              background: var(--bg-disabled) !important;
              border-color: var(--border-color-base) !important;
              color: var(--text-color-disabled) !important;
            }
          }
        }
      }
    }
  }

  // Fix any remaining white backgrounds
  div[style*='background-color: #fff'],
  div[style*='background-color: white'],
  div[style*='background: #fff'],
  div[style*='background: white'] {
    background: var(--component-background) !important;
  }

  // Calendar component dark mode fixes - Professional, modern, smart and intuitive UI
  .ant-picker-calendar {
    background: var(--component-background) !important;
    color: var(--text-color) !important;

    .ant-picker-panel {
      background: var(--component-background) !important;
      border: 0 !important;
      border-top: 1px solid var(--border-color-split) !important;
      border-radius: 0 !important;
      color: var(--text-color) !important;
    }

    .ant-picker-calendar-header {
      background: var(--component-background) !important;
      border-bottom: 1px solid var(--border-color-split) !important;
      color: var(--text-color) !important;

      .ant-picker-calendar-year-select,
      .ant-picker-calendar-month-select {
        color: var(--text-color) !important;
      }
    }

    .ant-picker-content {
      background: var(--component-background) !important;

      th {
        color: var(--text-color-secondary) !important;
        background: var(--bg-secondary) !important;
      }

      .ant-picker-cell {
        color: var(--text-color) !important;

        &:hover {
          background: var(--item-hover-bg) !important;
        }

        &.ant-picker-cell-selected {
          background: var(--primary-color-light) !important;

          .ant-picker-cell-inner {
            background: var(--primary-color) !important;
            color: var(--color-white) !important;
          }
        }

        &.ant-picker-cell-today {
          .ant-picker-cell-inner {
            border-color: var(--primary-color) !important;
            color: var(--primary-color) !important;
          }
        }

        &.ant-picker-cell-in-view {
          color: var(--text-color) !important;
        }

        &:not(.ant-picker-cell-in-view) {
          color: var(--text-color-disabled) !important;
        }
      }
    }

    .ant-picker-calendar-date {
      background: var(--component-background) !important;
      border-color: var(--border-color-split) !important;
      color: var(--text-color) !important;

      &:hover {
        background: var(--item-hover-bg) !important;
      }
    }

    .ant-picker-calendar-date-value {
      color: var(--text-color) !important;
    }

    .ant-picker-calendar-date-content {
      color: var(--text-color-secondary) !important;
    }
  }

  // Ant Design component overrides
  .ant-card {
    background: var(--bg-card) !important;
    border-color: var(--border-color-base) !important;
    color: var(--text-color) !important;

    .ant-card-head {
      background: var(--bg-card) !important;
      border-color: var(--border-color-base) !important;
      color: var(--text-color) !important;
    }

    .ant-card-body {
      background: var(--bg-card) !important;
      color: var(--text-color) !important;
    }
  }

  .ant-dropdown {
    .ant-dropdown-menu {
      background: var(--component-background) !important;
      border-color: var(--border-color-base) !important;
      box-shadow: var(--shadow-2) !important;

      .ant-dropdown-menu-item {
        color: var(--text-color) !important;

        &:hover {
          background: var(--bg-hover) !important;
          color: var(--primary-color) !important;
        }
      }
    }
  }

  // Form and input fixes
  .ant-form {
    .ant-form-item-label > label {
      color: var(--text-color) !important;
    }
  }

  .ant-input-number,
  .ant-select-selector {
    background: var(--input-background) !important;
    border-color: var(--border-color-base) !important;
    color: var(--text-color) !important;

    &:hover {
      border-color: var(--primary-color) !important;
    }

    &:focus {
      border-color: var(--primary-color) !important;
      box-shadow: 0 0 0 2px var(--primary-color-fade) !important;
    }
  }

  .sun-editor-editable {
    background-color: transparent !important;
    color: var(--text-color) !important;

    // Comprehensive HTML element coverage
    * {
      color: inherit !important;
      background-color: transparent !important;
    }

    // Headings
    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
      color: var(--text-color) !important;
      margin-bottom: 16px;
      font-weight: 600;
    }

    // Paragraphs and text elements
    p {
      color: var(--text-color-secondary) !important;
      margin-bottom: 12px;
      line-height: 1.6;
    }

    // Text formatting
    span,
    div {
      color: var(--text-color) !important;
    }

    // Lists
    ul,
    ol {
      color: var(--text-color-secondary) !important;
      margin-bottom: 12px;

      li {
        color: var(--text-color-secondary) !important;
        margin-bottom: 8px;
        line-height: 1.5;
      }
    }

    // Links
    a {
      color: var(--primary-color) !important;
      text-decoration: none;
      transition: all 0.3s ease;

      &:hover {
        color: var(--primary-color) !important;
        opacity: 0.8;
        text-decoration: underline;
      }

      &:visited {
        color: var(--primary-color) !important;
      }

      &:active {
        color: var(--primary-color) !important;
      }
    }

    // Text emphasis
    strong,
    b {
      color: var(--text-color) !important;
      font-weight: 600;
    }

    em,
    i {
      color: var(--text-color-secondary) !important;
      font-style: italic;
    }

    // Code elements
    code {
      color: var(--text-color) !important;
      padding: 2px 6px;
      font-family: 'Courier New', monospace;
    }

    pre {
      background: var(--bg-secondary) !important;
      color: var(--text-color) !important;
      padding: 16px;
      border-radius: 8px;
      border: 1px solid var(--border-color-base);
      overflow-x: auto;
      margin-bottom: 16px;

      code {
        background: transparent !important;
        border: none;
        padding: 0;
      }
    }

    // Blockquotes
    blockquote {
      background: var(--bg-secondary) !important;
      color: var(--text-color-secondary) !important;
      border-left: 4px solid var(--primary-color);
      padding: 16px 20px;
      margin: 16px 0;
      border-radius: 0 8px 8px 0;
      font-style: italic;

      p {
        color: var(--text-color-secondary) !important;
        margin-bottom: 0;
      }
    }

    // Tables
    table {
      border-collapse: collapse;
      width: 100%;
      margin-bottom: 16px;
      background: var(--component-background) !important;
      border: 1px solid var(--border-color-base);
      border-radius: 8px;
      overflow: hidden;

      th,
      td {
        color: var(--text-color) !important;
        padding: 12px 16px;
        text-align: left;
        border-bottom: 1px solid var(--border-color-split);
      }

      th {
        background: var(--bg-secondary) !important;
        font-weight: 600;
        color: var(--text-color) !important;
      }

      tr:hover {
        background: var(--item-hover-bg) !important;
      }
    }

    // Images
    img {
      max-width: 100%;
      height: auto;
      border-radius: 8px;
      margin: 8px 0;
      filter: var(--image-filter, none);
    }

    // Horizontal rules
    hr {
      border: none;
      height: 2px;
      background: var(--border-color-base);
      margin: 24px 0;
      border-radius: 1px;
    }

    // Form elements (if any)
    input,
    textarea,
    select {
      background: var(--component-background) !important;
      color: var(--text-color) !important;
      border: 1px solid var(--border-color-base) !important;
      border-radius: 4px;
      padding: 8px 12px;

      &:focus {
        border-color: var(--primary-color) !important;
        box-shadow: 0 0 0 2px var(--primary-color-light) !important;
      }
    }

    // Mark/highlight elements
    mark {
      background: var(--primary-color-light) !important;
      color: var(--text-color) !important;
      padding: 2px 4px;
      border-radius: 3px;
    }

    // Small text
    small {
      color: var(--text-color-tertiary) !important;
      font-size: 0.875em;
    }

    // Subscript and superscript
    sub,
    sup {
      color: var(--text-color-secondary) !important;
    }

    // Definition lists
    dl {
      margin-bottom: 16px;

      dt {
        color: var(--text-color) !important;
        font-weight: 600;
        margin-bottom: 4px;
      }

      dd {
        color: var(--text-color-secondary) !important;
        margin-bottom: 12px;
        margin-left: 20px;
      }
    }

    // Address elements
    address {
      color: var(--text-color-secondary) !important;
      font-style: italic;
      margin-bottom: 16px;
    }

    // Keyboard input
    kbd {
      background: var(--bg-secondary) !important;
      color: var(--text-color) !important;
      padding: 2px 6px;
      border-radius: 4px;
      border: 1px solid var(--border-color-base);
      font-family: 'Courier New', monospace;
      font-size: 0.875em;
    }

    // Sample output
    samp {
      background: var(--bg-secondary) !important;
      color: var(--text-color) !important;
      padding: 2px 6px;
      border-radius: 4px;
      font-family: 'Courier New', monospace;
    }

    // Variable text
    var {
      color: var(--primary-color) !important;
      font-style: italic;
      font-weight: 500;
    }

    // Deleted and inserted text
    del {
      color: var(--error-color) !important;
      text-decoration: line-through;
    }

    ins {
      color: var(--success-color) !important;
      text-decoration: underline;
      background: transparent !important;
    }

    // Abbreviations
    abbr {
      color: var(--text-color) !important;
      border-bottom: 1px dotted var(--text-color-tertiary);
      cursor: help;
    }

    // Citations
    cite {
      color: var(--text-color-secondary) !important;
      font-style: italic;
    }

    // Time elements
    time {
      color: var(--text-color-secondary) !important;
    }

    // Details and summary
    details {
      background: var(--bg-secondary) !important;
      border: 1px solid var(--border-color-base);
      border-radius: 8px;
      padding: 16px;
      margin-bottom: 16px;

      summary {
        color: var(--text-color) !important;
        font-weight: 600;
        cursor: pointer;
        margin-bottom: 8px;

        &:hover {
          color: var(--primary-color) !important;
        }
      }
    }

    // Figure and figcaption
    figure {
      margin: 16px 0;
      text-align: center;

      img {
        margin-bottom: 8px;
      }

      figcaption {
        color: var(--text-color-tertiary) !important;
        font-size: 0.875em;
        font-style: italic;
      }
    }
  }
}

// Contact form enhancements
.contact-form {
  background: var(--bg-card) !important;
  border-color: var(--border-color-base) !important;

  .ant-form-item {
    .ant-form-item-label > label {
      color: var(--text-color) !important;
      font-weight: 500;
    }

    .ant-input {
      background: var(--component-background) !important;
      border-color: var(--border-color-base) !important;
      color: var(--text-color) !important;
      border-radius: 8px;
      height: 48px;
      font-size: 14px;
      transition: all 0.3s ease;

      &:hover {
        border-color: var(--primary-color) !important;
        background: var(--bg-hover) !important;
      }

      &:focus {
        border-color: var(--primary-color) !important;
        box-shadow: 0 0 0 2px var(--primary-color-light) !important;
        background: var(--component-background) !important;
      }

      &::placeholder {
        color: var(--text-color-tertiary) !important;
        font-style: italic;
      }

      // Fix autofill styling in dark mode
      &:-webkit-autofill,
      &:-webkit-autofill:hover,
      &:-webkit-autofill:focus,
      &:-webkit-autofill:active {
        -webkit-box-shadow: 0 0 0 30px var(--component-background) inset !important;
        -webkit-text-fill-color: var(--text-color) !important;
        background-color: var(--component-background) !important;
        background-image: none !important;
        transition: background-color 5000s ease-in-out 0s;
      }

      // Fix for newer browsers
      &:-internal-autofill-selected {
        background-color: var(--component-background) !important;
        background-image: none !important;
        color: var(--text-color) !important;
      }
    }

    .ant-input.ant-input-status-error {
      border-color: var(--error-color) !important;
      background: var(--component-background) !important;

      &:hover {
        border-color: var(--error-color) !important;
      }

      &:focus {
        border-color: var(--error-color) !important;
        box-shadow: 0 0 0 2px rgba(240, 65, 52, 0.2) !important;
      }
    }

    textarea.ant-input {
      min-height: 120px;
      resize: vertical;
      line-height: 1.5;
      padding: 12px 16px;
    }

    .ant-form-item-explain-error {
      color: var(--error-color) !important;
      font-size: 12px;
      margin-top: 6px;
      font-weight: 500;
    }
  }

  .ant-btn {
    &.primary {
      background: var(--gradient-primary) !important;
      border: none !important;
      color: var(--color-white) !important;
      font-weight: 600;
      height: 48px;
      border-radius: 8px;
      font-size: 16px;
      transition: all 0.3s ease;
      box-shadow: var(--shadow-1);

      &:hover {
        background: var(--primary-color) !important;
      }

      &:active {
        transform: translateY(0);
      }

      &:disabled {
        background: var(--bg-disabled) !important;
        color: var(--text-color-disabled) !important;
        transform: none;
        box-shadow: none;
        cursor: not-allowed;
      }

      &[loading] {
        background: var(--bg-disabled) !important;
        color: var(--text-color-disabled) !important;
        transform: none;
        box-shadow: none;
      }
    }
  }
}

// Page heading improvements
.page-heading {
  color: var(--text-color) !important;
}

// Title styling improvements
.title {
  color: var(--primary-color) !important;
}

// Loading screen fixes
.loading-screen {
  background: var(--bg-white) !important;
  color: var(--text-color) !important;

  img,
  span {
    filter: var(--image-filter);
  }
}

// Empty state fixes
.ant-empty {
  color: var(--text-color-secondary) !important;

  .ant-empty-description {
    color: var(--text-color-secondary) !important;
  }

  .ant-empty-image {
    opacity: 0.6;
    filter: var(--image-filter);
  }
}

// List item hover fixes for dark mode
.ant-list-item {
  border-bottom-color: var(--border-color-split) !important;
  color: var(--text-color) !important;

  &:hover {
    background-color: var(--item-hover-bg) !important;
  }
}

// Message component dark mode fixes
.ant-message {
  .ant-message-notice {
    .ant-message-notice-content {
      background: var(--bg-modal) !important;
      color: var(--text-color) !important;
      box-shadow: var(--shadow-2) !important;
      border: 1px solid var(--border-color-base) !important;
    }
  }
}

// Messages/Chat interface dark mode fixes - Professional, modern, smart and intuitive UI
.conversation-list-item {
  background: var(--component-background) !important;
  color: var(--text-color) !important;
  border-bottom: 1px solid var(--border-color-split) !important;
  transition: all 0.3s ease !important;

  &:hover {
    background: var(--item-hover-bg) !important;
    transform: translateX(2px) !important;
  }

  &.active {
    background: var(--item-active-bg) !important;
    border-left: 3px solid var(--primary-color) !important;
  }

  .conversation-title {
    color: var(--text-color) !important;
    font-weight: 600 !important;
  }

  .conversation-snippet {
    color: var(--text-color-secondary) !important;
  }

  .conversation-photo {
    border: 2px solid var(--border-color-base) !important;
    transition: border-color 0.3s ease !important;
  }

  &:hover .conversation-photo {
    border-color: var(--primary-color-fade) !important;
  }
}

// Radio button group dark mode fixes for conversation filters
.ant-radio-group {
  background: var(--bg-secondary) !important;
  border: 1px solid var(--border-color-base) !important;
  border-radius: 8px !important;
  padding: 2px !important;
  box-shadow: var(--shadow-1) !important;

  .ant-radio-button-wrapper {
    background: transparent !important;
    border: none !important;
    color: var(--text-color-secondary) !important;
    border-radius: 6px !important;
    margin: 0 1px !important;
    transition: all 0.3s ease !important;
    font-weight: 500 !important;

    &:hover {
      background: var(--item-hover-bg) !important;
      color: var(--text-color) !important;
      transform: scale(1.02) !important;
    }

    &.ant-radio-button-wrapper-checked {
      background: var(--primary-color) !important;
      color: var(--color-white) !important;
      box-shadow: var(--shadow-2) !important;
      border: none !important;

      &:hover {
        background: var(--primary-color-hover) !important;
        transform: scale(1.02) !important;
      }
    }

    &:first-child {
      border-radius: 6px !important;
    }

    &:last-child {
      border-radius: 6px !important;
    }

    .anticon {
      color: inherit !important;
      margin-right: 4px !important;
    }
  }
}

// Badge component dark mode fixes for unread message count
.ant-badge {
  .ant-badge-count {
    background: var(--primary-color) !important;
    color: var(--color-white) !important;
    border: 1px solid var(--color-white) !important;
    box-shadow: var(--shadow-2) !important;
    font-weight: 600 !important;
  }

  .ant-badge-dot {
    background: var(--primary-color) !important;
    border: 2px solid var(--color-white) !important;
    box-shadow: var(--shadow-1) !important;
  }
}

// Conversation search input dark mode fixes
.conversation-search {
  background: var(--component-background) !important;
  border-bottom-color: var(--border-color-split) !important;

  input {
    background: var(--input-background) !important;
    color: var(--text-color) !important;
    border-color: var(--border-color-base) !important;

    &::placeholder {
      color: var(--text-color-tertiary) !important;
    }

    &:hover {
      border-color: var(--primary-color) !important;
      box-shadow: 0 0 0 2px var(--primary-color-light) !important;
    }

    &:focus {
      border-color: var(--primary-color) !important;
      box-shadow: 0 0 0 3px var(--primary-color-light) !important;
      background: var(--component-background) !important;
    }
  }

  .anticon-search {
    color: var(--text-color-secondary) !important;
  }
}

// Notification dark mode fixes
.ant-notification {
  .ant-notification-notice {
    background: var(--bg-modal) !important;
    border: 1px solid var(--border-color-base) !important;
    box-shadow: var(--shadow-3) !important;

    .ant-notification-notice-message {
      color: var(--text-color) !important;
    }

    .ant-notification-notice-description {
      color: var(--text-color-secondary) !important;
    }

    .ant-notification-notice-close {
      color: var(--text-color-secondary) !important;

      &:hover {
        color: var(--primary-color) !important;
      }
    }
  }
}

// Promotion form dark mode fixes - Professional, modern, smart and intuitive UI
.promotion-form-card {
  background: var(--card-background) !important;
  border-color: var(--border-color-base) !important;
  box-shadow: var(--shadow-2) !important;

  &:hover {
    box-shadow: var(--shadow-3) !important;
  }

  .ant-card {
    background: transparent !important;
    border: none !important;
  }

  .ant-card-body {
    background: transparent !important;
    color: var(--text-color) !important;
  }

  // Typography components
  .ant-typography {
    color: var(--text-color) !important;

    &.ant-typography-title {
      color: var(--text-color) !important;

      .anticon {
        color: var(--primary-color) !important;
      }
    }

    &[type='secondary'] {
      color: var(--text-color-secondary) !important;
    }
  }

  // Alert components
  .ant-alert {
    background: var(--primary-color-light) !important;
    border: 1px solid var(--primary-color-fade) !important;
    border-radius: 10px !important;
    color: var(--text-color) !important;

    .ant-alert-message {
      color: var(--text-color) !important;
      font-weight: 600 !important;
    }

    .ant-alert-description {
      color: var(--text-color-secondary) !important;
    }

    .ant-alert-icon {
      color: var(--primary-color) !important;
    }

    &.ant-alert-info {
      background: var(--primary-color-light) !important;
      border-color: var(--primary-color-fade) !important;
    }
  }

  // Input components
  .ant-input-number {
    background: var(--input-background) !important;
    border-color: var(--border-color-base) !important;
    color: var(--text-color) !important;

    .ant-input-number-input {
      background: transparent !important;
      color: var(--text-color) !important;

      &::placeholder {
        color: var(--text-color-tertiary) !important;
      }
    }

    .ant-input-number-handler-wrap {
      background: var(--input-background) !important;
      border-left-color: var(--border-color-base) !important;

      .ant-input-number-handler {
        color: var(--text-color-secondary) !important;
        border-color: var(--border-color-base) !important;

        &:hover {
          background: var(--item-hover-bg) !important;
          color: var(--primary-color) !important;
        }
      }
    }

    &:hover {
      border-color: var(--primary-color) !important;
      box-shadow: 0 0 0 2px var(--primary-color-light) !important;

      .ant-input-number-handler-wrap {
        border-left-color: var(--primary-color) !important;
      }
    }

    &:focus-within,
    &.ant-input-number-focused {
      border-color: var(--primary-color) !important;
      box-shadow: 0 0 0 3px var(--primary-color-light) !important;
    }
  }

  // Input number group addon (for "subscribers", "days" text)
  .ant-input-number-group-addon {
    background: var(--bg-secondary) !important;
    border-color: var(--border-color-base) !important;
    color: var(--text-color-secondary) !important;
    font-weight: 500 !important;
    font-size: 13px !important;
    transition: all 0.3s ease !important;

    &:hover {
      background: var(--item-hover-bg) !important;
      color: var(--text-color) !important;
    }
  }

  // Input number affix wrapper disabled state dark mode fix
  .ant-input-number-affix-wrapper-disabled {
    color: var(--text-color-disabled) !important;
    background-color: var(--bg-disabled) !important;
    border-color: var(--border-color-split) !important;
    box-shadow: none !important;
    cursor: not-allowed !important;
    opacity: 0.6 !important;

    .ant-input-number-input {
      color: var(--text-color-disabled) !important;
      background: transparent !important;
      cursor: not-allowed !important;
    }

    .ant-input-number-handler-wrap {
      background: var(--bg-disabled) !important;
      border-left-color: var(--border-color-split) !important;

      .ant-input-number-handler {
        color: var(--text-color-disabled) !important;
        background: transparent !important;
        cursor: not-allowed !important;
        border-color: var(--border-color-split) !important;

        &:hover {
          background: transparent !important;
          color: var(--text-color-disabled) !important;
          cursor: not-allowed !important;
        }
      }
    }

    &:hover {
      border-color: var(--border-color-split) !important;
      box-shadow: none !important;
    }
  }

  // Select components
  .ant-select {
    .ant-select-selector {
      background: var(--input-background) !important;
      border-color: var(--border-color-base) !important;
      color: var(--text-color) !important;

      .ant-select-selection-item {
        color: var(--text-color) !important;
      }

      .ant-select-selection-placeholder {
        color: var(--text-color-tertiary) !important;
      }
    }

    &:hover .ant-select-selector {
      border-color: var(--primary-color) !important;
      box-shadow: 0 0 0 2px var(--primary-color-light) !important;
    }

    &.ant-select-focused .ant-select-selector {
      border-color: var(--primary-color) !important;
      box-shadow: 0 0 0 3px var(--primary-color-light) !important;
    }

    .ant-select-arrow {
      color: var(--text-color-secondary) !important;
    }
  }

  // Additional specificity for select selection items to ensure dark mode text visibility
  .ant-select .ant-select-selector .ant-select-selection-item {
    color: var(--text-color) !important;
  }

  // Fix for multiple select selection items with hardcoded colors
  .ant-select-multiple .ant-select-selection-item {
    background: var(--bg-secondary) !important;
    border: 1px solid var(--border-color-base) !important;
    color: var(--text-color) !important;

    // Close button styling for selected items
    .ant-select-selection-item-remove {
      color: var(--text-color-secondary) !important;

      &:hover {
        color: var(--error-color) !important;
        background: rgba(255, 82, 82, 0.1) !important;
      }
    }
  }

  // Radio button components
  .ant-radio-group {
    background: var(--bg-secondary) !important;
    border-color: var(--border-color-base) !important;
  }

  .ant-radio-button-wrapper {
    background: transparent !important;
    border: none !important;
    color: var(--text-color-secondary) !important;

    &:hover {
      background: var(--item-hover-bg) !important;
      color: var(--text-color) !important;
    }

    &.ant-radio-button-wrapper-checked {
      background: var(--primary-color) !important;
      color: var(--color-white) !important;
      box-shadow: var(--shadow-1) !important;
    }

    &:first-child {
      border-radius: 8px !important;
    }

    &:last-child {
      border-radius: 8px !important;
    }
  }

  // Button components
  .ant-btn {
    &:not(.ant-btn-primary) {
      background: var(--button-background) !important;
      border-color: var(--border-color-base) !important;
      color: var(--text-color) !important;

      &:hover {
        background: var(--item-hover-bg) !important;
        border-color: var(--primary-color) !important;
        color: var(--primary-color) !important;
      }
    }

    &.ant-btn-primary {
      background: var(--gradient-primary) !important;
      border: none !important;
      color: var(--color-white) !important;
    }
  }

  // Space component
  .ant-space {
    .ant-space-item {
      color: var(--text-color) !important;
    }
  }
}

.promotion-header {
  border-bottom-color: var(--border-color-split) !important;
}

.promotion-stats-badge {
  background: var(--bg-secondary) !important;
  border-color: var(--border-color-base) !important;
  color: var(--text-color) !important;
  box-shadow: var(--shadow-1) !important;

  &:hover {
    background: var(--item-hover-bg) !important;
    box-shadow: var(--shadow-2) !important;
  }
}

.stat-item {
  background: var(--bg-secondary) !important;
  border-color: var(--border-color-base) !important;

  &::before {
    background: var(--gradient-secondary) !important;
  }

  &:hover {
    background: var(--item-hover-bg) !important;
    box-shadow: var(--shadow-1) !important;
  }
}

.stat-icon {
  box-shadow: var(--shadow-1) !important;

  &.success {
    color: var(--success-color) !important;
    background: rgba(16, 209, 58, 0.15) !important;
    border-color: rgba(16, 209, 58, 0.3) !important;
  }

  &.primary {
    color: var(--primary-color) !important;
    background: var(--primary-color-light) !important;
    border-color: var(--primary-color-fade) !important;
  }
}

.stat-content {
  .ant-typography {
    &.ant-typography-strong {
      color: var(--text-color) !important;
    }

    &[type='secondary'] {
      color: var(--text-color-secondary) !important;
    }
  }
}

// Promotion inactive state
.promotion-inactive {
  .ant-alert {
    background: var(--info-color) !important;
    background: var(--primary-color-light) !important;
    border-color: var(--primary-color-fade) !important;
    color: var(--text-color) !important;

    .ant-alert-message {
      color: var(--text-color) !important;
      font-weight: 600 !important;
    }

    .ant-alert-description {
      color: var(--text-color) !important;
    }

    .ant-alert-icon {
      color: var(--primary-color) !important;
    }
  }
}

// Verification form dark mode fixes
.account-form {
  color: var(--text-color) !important;

  .ant-form-item {
    .ant-form-item-label {
      label {
        color: var(--text-color) !important;
        font-weight: 600;
      }
    }

    .ant-form-item-explain {
      color: var(--text-color-secondary) !important;
    }

    .ant-form-item-extra {
      color: var(--text-color-tertiary) !important;
    }
  }

  // Creator/Model photo verification styling
  .creator-photo-verification,
  .model-photo-verification {
    .ant-form-item-label {
      label {
        color: var(--text-color) !important;
        font-weight: 600;
        font-size: 14px;
      }
    }

    .ant-form-item-explain {
      color: var(--text-color-secondary) !important;
      font-size: 12px;
      text-align: center;
      margin-top: 8px;
    }
  }

  // Document upload container styling
  .document-upload {
    background: var(--bg-card) !important;
    border: 2px dashed var(--border-color-base) !important;
    border-radius: 12px !important;
    padding: 20px !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: var(--gradient-secondary);
      opacity: 0.05;
      pointer-events: none;
    }

    &:hover {
      border-color: var(--primary-color) !important;
      background: var(--bg-hover) !important;
      transform: translateY(-2px);
      box-shadow: var(--shadow-2) !important;
    }

    // Image styling within document upload
    img {
      border-radius: 8px !important;
      box-shadow: var(--shadow-1) !important;
      transition: all 0.3s ease !important;
      border: 1px solid var(--border-color-base) !important;
      background: var(--component-background) !important;

      &:hover {
        transform: scale(1.02);
        box-shadow: var(--shadow-2) !important;
      }
    }

    // Ant Design Image component styling
    .ant-image {
      border-radius: 8px !important;
      overflow: hidden;
      box-shadow: var(--shadow-1) !important;
      border: 1px solid var(--border-color-base) !important;

      img {
        border: none !important;
        box-shadow: none !important;
      }

      &:hover {
        transform: scale(1.02);
        box-shadow: var(--shadow-2) !important;
      }
    }
  }

  // Alert component styling within forms
  .ant-alert {
    background: var(--bg-card) !important;
    border: 1px solid var(--border-color-base) !important;
    border-radius: 8px !important;
    color: var(--text-color) !important;
    box-shadow: var(--shadow-1) !important;

    .ant-alert-message {
      color: var(--text-color) !important;

      a {
        color: var(--primary-color) !important;
        text-decoration: none;
        font-weight: 500;
        transition: all 0.2s ease;

        &:hover {
          color: var(--primary-color) !important;
          opacity: 0.8;
          text-decoration: underline;
        }
      }
    }

    &.ant-alert-info {
      background: var(--primary-color-light) !important;
      border-color: var(--primary-color-fade) !important;

      .ant-alert-icon {
        color: var(--primary-color) !important;
      }
    }

    &.ant-alert-success {
      background: rgba(16, 209, 58, 0.1) !important;
      border-color: rgba(16, 209, 58, 0.3) !important;

      .ant-alert-icon {
        color: var(--success-color) !important;
      }
    }

    &.ant-alert-warning {
      background: rgba(255, 193, 7, 0.1) !important;
      border-color: rgba(255, 193, 7, 0.3) !important;

      .ant-alert-icon {
        color: var(--warning-color) !important;
      }
    }

    &.ant-alert-error {
      background: rgba(255, 82, 82, 0.1) !important;
      border-color: rgba(255, 82, 82, 0.3) !important;

      .ant-alert-icon {
        color: var(--error-color) !important;
      }
    }
  }
}

// Enhanced image preview for dark mode
.ant-image-preview-mask {
  background-color: var(--bg-overlay) !important;
}

.ant-image-preview-wrap {
  .ant-image-preview-operations {
    background: var(--bg-modal) !important;
    border: 1px solid var(--border-color-base) !important;
    border-radius: 8px !important;
    box-shadow: var(--shadow-2) !important;

    .ant-image-preview-operations-operation {
      color: var(--text-color) !important;

      &:hover {
        color: var(--primary-color) !important;
      }
    }
  }

  .ant-image-preview-close {
    color: var(--color-white) !important;
    background: var(--bg-modal) !important;
    border: 1px solid var(--border-color-base) !important;
    border-radius: 50% !important;
    box-shadow: var(--shadow-2) !important;

    &:hover {
      color: var(--primary-color) !important;
      background: var(--component-background) !important;
    }
  }
}

// Additional Ant Design component dark mode fixes for promotion form
.ant-dropdown {
  .ant-dropdown-menu {
    background: var(--bg-modal) !important;
    border: 1px solid var(--border-color-base) !important;
    box-shadow: var(--shadow-3) !important;
    border-radius: 8px !important;

    .ant-dropdown-menu-item {
      color: var(--text-color) !important;

      &:hover {
        background: var(--item-hover-bg) !important;
        color: var(--primary-color) !important;
      }

      &.ant-dropdown-menu-item-selected {
        background: var(--primary-color-light) !important;
        color: var(--primary-color) !important;
      }
    }

    .ant-dropdown-menu-item-divider {
      background: var(--border-color-split) !important;
    }
  }
}

// Select dropdown options
.ant-select-dropdown {
  background: var(--bg-modal) !important;
  border: 1px solid var(--border-color-base) !important;
  box-shadow: var(--shadow-3) !important;
  border-radius: 8px !important;

  .ant-select-item {
    color: var(--text-color) !important;

    &:hover {
      background: var(--item-hover-bg) !important;
    }

    &.ant-select-item-option-selected {
      background: var(--primary-color-light) !important;
      color: var(--primary-color) !important;
      font-weight: 600 !important;
    }

    &.ant-select-item-option-active {
      background: var(--item-hover-bg) !important;
    }
  }

  .ant-select-item-empty {
    color: var(--text-color-tertiary) !important;
  }
}

// Tooltip dark mode fixes
.ant-tooltip {
  .ant-tooltip-inner {
    background: var(--bg-modal) !important;
    color: var(--text-color) !important;
    border: 1px solid var(--border-color-base) !important;
    box-shadow: var(--shadow-2) !important;
    border-radius: 6px !important;
  }

  .ant-tooltip-arrow {
    &::before {
      background: var(--bg-modal) !important;
      border-color: var(--border-color-base) !important;
    }
  }
}

// Form validation message styling
.ant-form-item-explain-error {
  color: var(--error-color) !important;
  background: rgba(255, 82, 82, 0.1) !important;
  padding: 6px 10px !important;
  border-radius: 4px !important;
  border-left: 3px solid var(--error-color) !important;
  margin-top: 6px !important;
}

.ant-form-item-explain-success {
  color: var(--success-color) !important;
  background: rgba(16, 209, 58, 0.1) !important;
  padding: 6px 10px !important;
  border-radius: 4px !important;
  border-left: 3px solid var(--success-color) !important;
  margin-top: 6px !important;
}

.ant-form-item-explain-warning {
  color: var(--warning-color) !important;
  background: rgba(255, 193, 7, 0.1) !important;
  padding: 6px 10px !important;
  border-radius: 4px !important;
  border-left: 3px solid var(--warning-color) !important;
  margin-top: 6px !important;
}

// Loading and spin components
.ant-spin {
  .ant-spin-dot {
    .ant-spin-dot-item {
      background-color: var(--primary-color) !important;
    }
  }

  .ant-spin-text {
    color: var(--text-color-secondary) !important;
  }
}

// Skeleton loading animation for dark mode
@keyframes ant-skeleton-loading {
  0% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0 50%;
  }
}
