// Import theme and variables
@import './vars.less';
@import './theme.less';

.dark-mode {
  // Layout components
  .ant-layout {
    background-color: var(--bg-white);
  }

  // Table components
  .custom-table {
    .ant-table-thead > tr > th {
      background: var(--bg-secondary) !important;
      color: var(--text-color) !important;
    }
    .ant-table-tbody > tr:hover > td {
      background: var(--item-hover-bg) !important;
    }
  }

  // Content components
  .stat-content {
    color: var(--text-color);
  }

  .media-preview-item {
    background: var(--component-background);
    border-color: var(--border-color-base);
  }

  .contact-content {
    background: var(--bg-card) !important;
  } // Component styles begin here...
}

// Smooth transitions for theme changes
.theme-transition {
  * {
    transition: background-color 0.3s ease, color 0.3s ease,
      border-color 0.3s ease, box-shadow 0.3s ease !important;
  }
}

// Dark mode specific overrides for components
.dark-mode {
  // Global anticon dark mode fixes - Professional, modern, smart and intuitive UI
  .anticon {
    transition: color 0.3s ease !important;

    // Specific icon color overrides for better dark mode support
    &.anticon-search {
      color: var(--text-color-secondary) !important;
    }

    &.anticon-close {
      color: var(--text-color-secondary) !important;

      &:hover {
        color: var(--error-color) !important;
      }
    }

    &.anticon-check {
      color: var(--success-color) !important;
    }

    &.anticon-exclamation-circle,
    &.anticon-warning {
      color: var(--warning-color) !important;
    }

    &.anticon-info-circle {
      color: var(--info-color) !important;
    }

    &.anticon-question-circle {
      color: var(--primary-color) !important;
    }

    // Button icons should inherit from their parent button
    .ant-btn & {
      color: inherit !important;
    }

    // Form label icons
    .ant-form-item-label & {
      color: var(--primary-color) !important;
    }

    // Alert icons
    .ant-alert & {
      color: inherit !important;
    }

    // Menu icons
    .ant-menu & {
      color: inherit !important;
    }

    // Dropdown icons
    .ant-dropdown & {
      color: var(--text-color-secondary) !important;
    }

    // Table action icons
    .ant-table & {
      color: var(--text-color-secondary) !important;

      &:hover {
        color: var(--primary-color) !important;
      }
    }

    // Card action icons
    .ant-card & {
      color: var(--text-color-secondary) !important;

      &:hover {
        color: var(--primary-color) !important;
      }
    }

    // Input suffix/prefix icons
    .ant-input-suffix &,
    .ant-input-prefix & {
      color: var(--text-color-secondary) !important;
    }

    // Select arrow icons
    .ant-select-arrow & {
      color: var(--text-color-secondary) !important;
    }

    // Pagination icons
    .ant-pagination & {
      color: var(--text-color-secondary) !important;

      &:hover {
        color: var(--primary-color) !important;
      }
    }

    // Breadcrumb icons
    .ant-breadcrumb & {
      color: var(--text-color-secondary) !important;
    }

    // Steps icons
    .ant-steps & {
      color: inherit !important;
    }

    // Upload icons
    .ant-upload & {
      color: var(--text-color-secondary) !important;

      &:hover {
        color: var(--primary-color) !important;
      }
    }

    // Tree icons
    .ant-tree & {
      color: var(--text-color-secondary) !important;
    }

    // Collapse icons
    .ant-collapse & {
      color: var(--text-color-secondary) !important;
    }

    // Tabs icons
    .ant-tabs & {
      color: var(--text-color-secondary) !important;
    }

    // Modal icons
    .ant-modal & {
      color: var(--text-color-secondary) !important;
    }

    // Drawer icons
    .ant-drawer & {
      color: var(--text-color-secondary) !important;
    }

    // Notification icons
    .ant-notification & {
      color: inherit !important;
    }

    // Notification dropdown specific icons
    .notification-menu & {
      color: var(--text-color-secondary) !important;

      &:hover {
        color: var(--primary-color) !important;
      }
    }

    // Message icons
    .ant-message & {
      color: inherit !important;
    }

    // Popover/Tooltip icons
    .ant-popover &,
    .ant-tooltip & {
      color: var(--text-color-secondary) !important;
    }

    // Calendar icons
    .ant-calendar &,
    .ant-picker & {
      color: var(--text-color-secondary) !important;
    }

    // Transfer icons
    .ant-transfer & {
      color: var(--text-color-secondary) !important;
    }

    // List icons
    .ant-list & {
      color: var(--text-color-secondary) !important;

      &:hover {
        color: var(--primary-color) !important;
      }
    }

    // Avatar icons
    .ant-avatar & {
      color: inherit !important;
    }

    // Badge icons
    .ant-badge & {
      color: inherit !important;
    }

    // Tag icons
    .ant-tag & {
      color: inherit !important;
    }

    // Timeline icons
    .ant-timeline & {
      color: var(--primary-color) !important;
    }

    // Rate icons
    .ant-rate & {
      color: var(--warning-color) !important;
    }

    // Switch icons
    .ant-switch & {
      color: var(--color-white) !important;
    }

    // Slider icons
    .ant-slider & {
      color: var(--primary-color) !important;
    }

    // Progress icons
    .ant-progress & {
      color: inherit !important;
    }

    // Spin icons
    .ant-spin & {
      color: var(--primary-color) !important;
    }

    // Back top icons
    .ant-back-top & {
      color: var(--color-white) !important;
    }

    // Anchor icons
    .ant-anchor & {
      color: var(--text-color-secondary) !important;

      &:hover {
        color: var(--primary-color) !important;
      }
    }

    // Affix icons
    .ant-affix & {
      color: inherit !important;
    }
  }

  // Message components
  .conversation-search input {
    background-color: var(--bg-secondary);
    color: var(--text-color);
    border-color: var(--border-color-base);
  }

  // Conversation list item dark mode enhancements
  .conversation-list-item {
    // Ensure proper background transition
    transition: background-color 0.2s ease, border-color 0.2s ease !important;

    &:hover {
      background-color: var(--item-hover-bg) !important;
      // Add subtle border for better definition
      border-left: 3px solid transparent;
      border-left-color: rgba(255, 255, 255, 0.1);
    }

    &.active {
      background-color: var(--item-active-bg) !important;
      border-left-color: var(--primary-color) !important;

      // Add subtle glow effect for active state
      box-shadow: inset 3px 0 0 var(--primary-color),
        0 0 0 1px rgba(139, 109, 209, 0.1);
    }

    // Ensure text remains visible
    .conversation-title {
      color: var(--text-color) !important;
    }

    .conversation-snippet {
      color: var(--text-color-secondary) !important;
    }

    .conversation-time {
      color: var(--text-color-tertiary) !important;
    }
  }

  .compose-input {
    background-color: var(--bg-secondary) !important;
    color: var(--text-color) !important;

    &::placeholder {
      color: var(--text-color-tertiary) !important;
      opacity: 1 !important;
    }

    // Ant Design input overrides for compose components
    &.ant-input {
      background-color: var(--bg-secondary) !important;
      color: var(--text-color) !important;
      border: none !important;

      &::placeholder {
        color: var(--text-color-tertiary) !important;
        opacity: 1 !important;
      }
    }
  }

  // Specific overrides for messaging compose inputs
  .compose .compose-input,
  .compose-box .compose-input {
    background-color: var(--bg-secondary) !important;
    color: var(--text-color) !important;

    &::placeholder {
      color: var(--text-color-tertiary) !important;
      opacity: 1 !important;
    }

    &.ant-input {
      background-color: var(--bg-secondary) !important;
      color: var(--text-color) !important;

      &::placeholder {
        color: var(--text-color-tertiary) !important;
        opacity: 1 !important;
      }
    }
  }

  // Gallery components
  .btn-select-gallery {
    background-color: var(--bg-secondary);
    border-color: var(--border-color-base);
    color: var(--text-color);

    &:hover {
      background-color: var(--bg-hover);
      border-color: var(--primary-color);
    }

    &::before {
      color: var(--primary-color);
    }

    &::after {
      color: var(--text-color-secondary);
    }
  }

  // Video.js components
  .video-js {
    .vjs-audio {
      color: var(--color-white);

      &.vjs-audio-off {
        color: var(--error-color);
      }
    }
  }

  // Form components
  .ant-form-item-explain-error {
    color: var(--error-color) !important;
  }

  // Store components
  .prod-price {
    color: var(--primary-color);

    .dc-price {
      color: var(--error-color);
    }
  }

  // Search components
  .page-search {
    color: var(--text-color);
  }

  // Emoji picker dark mode fixes - Professional, modern, smart and intuitive UI
  aside.emoji-picker-react {
    background: var(--bg-modal) !important;
    border: 1px solid var(--border-color-base) !important;
    box-shadow: var(--shadow-2) !important;
    color: var(--text-color) !important;

    // Emoji picker header
    .emoji-picker-react-header {
      background: var(--bg-secondary) !important;
      border-bottom: 1px solid var(--border-color-base) !important;
      color: var(--text-color) !important;
    }

    // Emoji picker categories
    .emoji-picker-react-categories {
      background: var(--bg-secondary) !important;
      border-bottom: 1px solid var(--border-color-base) !important;

      .emoji-picker-react-category {
        color: var(--text-color-secondary) !important;

        &:hover {
          background: var(--bg-hover) !important;
          color: var(--primary-color) !important;
        }

        &.emoji-picker-react-category-active {
          background: var(--primary-color) !important;
          color: var(--color-white) !important;
        }
      }
    }

    // Emoji picker body
    .emoji-picker-react-body {
      background: var(--bg-modal) !important;
      color: var(--text-color) !important;

      .emoji-picker-react-emoji {
        &:hover {
          background: var(--bg-hover) !important;
        }
      }
    }

    // Emoji picker search
    .emoji-picker-react-search {
      background: var(--bg-secondary) !important;
      border-bottom: 1px solid var(--border-color-base) !important;

      input {
        background: var(--input-background) !important;
        border: 1px solid var(--border-color-base) !important;
        color: var(--text-color) !important;

        &::placeholder {
          color: var(--text-color-tertiary) !important;
        }

        &:focus {
          border-color: var(--primary-color) !important;
          box-shadow: 0 0 0 2px var(--primary-color-light) !important;
        }
      }
    }

    // Emoji picker scrollbar
    .emoji-picker-react-scroll {
      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: var(--bg-secondary);
      }

      &::-webkit-scrollbar-thumb {
        background: var(--text-color-tertiary);
        border-radius: 3px;

        &:hover {
          background: var(--text-color-secondary);
        }
      }
    }

    // Additional emoji picker elements for comprehensive coverage
    .emoji-picker-react-group {
      background: var(--bg-modal) !important;
      color: var(--text-color) !important;

      .emoji-picker-react-group-header {
        background: var(--bg-secondary) !important;
        color: var(--text-color-secondary) !important;
        border-bottom: 1px solid var(--border-color-base) !important;
      }
    }

    // Emoji picker category buttons
    .emoji-picker-react-category-button {
      background: transparent !important;
      color: var(--text-color-secondary) !important;
      border: none !important;
      transition: all 0.3s ease !important;

      &:hover {
        background: var(--bg-hover) !important;
        color: var(--primary-color) !important;
      }

      &.emoji-picker-react-category-button-active {
        background: var(--primary-color) !important;
        color: var(--color-white) !important;
      }
    }

    // Emoji picker emoji buttons
    .emoji-picker-react-emoji-button {
      background: transparent !important;
      border: none !important;
      transition: background-color 0.2s ease !important;

      &:hover {
        background: var(--bg-hover) !important;
      }

      &:focus {
        outline: 2px solid var(--primary-color) !important;
        outline-offset: 2px !important;
      }
    }

    // Emoji picker footer (if present)
    .emoji-picker-react-footer {
      background: var(--bg-secondary) !important;
      border-top: 1px solid var(--border-color-base) !important;
      color: var(--text-color-secondary) !important;
    }

    // Emoji picker skin tone selector (if enabled)
    .emoji-picker-react-skin-tone-selector {
      background: var(--bg-modal) !important;
      border: 1px solid var(--border-color-base) !important;
      box-shadow: var(--shadow-2) !important;

      .emoji-picker-react-skin-tone-option {
        &:hover {
          background: var(--bg-hover) !important;
        }

        &.emoji-picker-react-skin-tone-option-selected {
          background: var(--primary-color) !important;
        }
      }
    }

    // Emoji group headers with sticky positioning - Fix for hardcoded colors
    .emoji-group:before {
      color: var(--text-color-secondary) !important;
      background: var(--bg-component) !important;
      backdrop-filter: blur(8px) !important;
      -webkit-backdrop-filter: blur(8px) !important;
    }

    // Alternative selector for emoji group headers
    .emoji-picker-react .emoji-group:before {
      color: var(--text-color-secondary) !important;
      background: var(--bg-component) !important;
      backdrop-filter: blur(8px) !important;
      -webkit-backdrop-filter: blur(8px) !important;
    }
  }

  // Vault page specific dark mode fixes - Professional, modern, smart and intuitive UI
  .vault-card {
    background: var(--bg-card) !important;
    border-color: var(--border-color-base) !important;
    box-shadow: var(--shadow-1) !important;
    transition: all 0.3s ease !important;

    &:hover {
      border-color: var(--primary-color-fade) !important;
      box-shadow: var(--shadow-3) !important;
    }

    .ant-card-body {
      background: var(--bg-card) !important;
    }
  }

  // Page header dark mode fixes
  .ant-page-header {
    background: transparent !important;
    color: var(--text-color) !important;

    .ant-page-header-heading-title {
      color: var(--text-color) !important;
    }

    .ant-page-header-back-button {
      color: var(--text-color-secondary) !important;
      transition: all 0.3s ease !important;

      &:hover {
        color: var(--primary-color) !important;
      }
    }
  }

  // Typography dark mode fixes
  .ant-typography {
    color: var(--text-color) !important;

    &.ant-typography-title {
      color: var(--text-color) !important;
    }

    &[type='secondary'] {
      color: var(--text-color-secondary) !important;
    }

    &[type='success'] {
      color: var(--success-color) !important;
    }

    &[type='warning'] {
      color: var(--warning-color) !important;
    }

    &[type='danger'] {
      color: var(--error-color) !important;
    }
  }

  // Space component dark mode fixes
  .ant-space {
    color: var(--text-color) !important;

    .ant-space-item {
      color: inherit !important;
    }
  }

  // Image component dark mode fixes
  .ant-image {
    border-radius: 8px !important;
    overflow: hidden !important;
    transition: all 0.3s ease !important;

    &:hover {
      transform: scale(1.02) !important;
    }

    .ant-image-img {
      transition: all 0.3s ease !important;
    }

    .ant-image-mask {
      background: var(--bg-overlay) !important;
      backdrop-filter: blur(4px) !important;

      .ant-image-mask-info {
        color: var(--color-white) !important;

        .anticon {
          color: var(--color-white) !important;
        }
      }
    }
  }

  // Skeleton dark mode fixes
  .ant-skeleton {
    .ant-skeleton-title {
      background: linear-gradient(
        90deg,
        var(--bg-disabled) 25%,
        var(--bg-hover) 37%,
        var(--bg-disabled) 63%
      ) !important;
    }

    .ant-skeleton-paragraph li {
      background: linear-gradient(
        90deg,
        var(--bg-disabled) 25%,
        var(--bg-hover) 37%,
        var(--bg-disabled) 63%
      ) !important;
    }

    .ant-skeleton-avatar {
      background: linear-gradient(
        90deg,
        var(--bg-disabled) 25%,
        var(--bg-hover) 37%,
        var(--bg-disabled) 63%
      ) !important;
    }

    .ant-skeleton-button {
      background: linear-gradient(
        90deg,
        var(--bg-disabled) 25%,
        var(--bg-hover) 37%,
        var(--bg-disabled) 63%
      ) !important;
    }

    .ant-skeleton-input {
      background: linear-gradient(
        90deg,
        var(--bg-disabled) 25%,
        var(--bg-hover) 37%,
        var(--bg-disabled) 63%
      ) !important;
    }
  }

  // Form dark mode fixes
  .ant-form {
    .ant-form-item {
      .ant-form-item-label {
        label {
          color: var(--text-color) !important;
          font-weight: 600 !important;
        }
      }

      .ant-form-item-explain {
        color: var(--text-color-secondary) !important;
      }

      .ant-form-item-extra {
        color: var(--text-color-tertiary) !important;
      }

      .ant-form-item-explain-error {
        color: var(--error-color) !important;
      }
    }
  }

  // Button dark mode fixes for vault page
  .ant-btn {
    border-radius: 8px !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;

    &.ant-btn-default {
      border-color: var(--border-color-base) !important;
      color: var(--text-color) !important;

      &:hover {
        background: var(--bg-hover) !important;
        border-color: var(--primary-color) !important;
        color: var(--primary-color) !important;
      }

      &:focus {
        background: var(--bg-hover) !important;
        border-color: var(--primary-color) !important;
        color: var(--primary-color) !important;
      }
    }

    &.ant-btn-primary {
      background: var(--primary-color) !important;
      border-color: var(--primary-color) !important;
      color: var(--color-white) !important;

      &:hover {
        background: var(--primary-color) !important;
        border-color: var(--primary-color) !important;
        opacity: 0.9 !important;
      }

      &:focus {
        background: var(--primary-color) !important;
        border-color: var(--primary-color) !important;
        opacity: 0.9 !important;
      }
    }

    &.ant-btn-text {
      color: var(--text-color-secondary) !important;
      background: transparent !important;
      border: none !important;

      &:hover {
        background: var(--bg-hover) !important;
        color: var(--primary-color) !important;
      }

      &.ant-btn-dangerous {
        color: var(--error-color) !important;

        &:hover {
          background: rgba(255, 77, 79, 0.1) !important;
          color: var(--error-color) !important;
        }
      }
    }

    &[disabled] {
      background: var(--bg-disabled) !important;
      border-color: var(--border-color-split) !important;
      color: var(--text-color-disabled) !important;
      opacity: 0.6 !important;
    }
  }

  // Table dark mode fixes for VaultTable
  .ant-table {
    background: var(--bg-card) !important;
    color: var(--text-color) !important;
    border-radius: 12px !important;
    overflow: hidden !important;

    .ant-table-container {
      border-radius: 12px !important;
    }

    .ant-table-thead > tr > th {
      background: var(--bg-secondary) !important;
      border-bottom-color: var(--border-color-split) !important;
      color: var(--text-color) !important;
      font-weight: 600 !important;

      &:not(:last-child):not(.ant-table-selection-column):not(
          .ant-table-row-expand-icon-cell
        ):not([colspan])::before {
        background-color: var(--border-color-split) !important;
      }

      .ant-table-column-sorter {
        color: var(--text-color-tertiary) !important;

        &.ant-table-column-sorter-up.active,
        &.ant-table-column-sorter-down.active {
          color: var(--primary-color) !important;
        }
      }
    }

    .ant-table-tbody > tr > td {
      border-bottom-color: var(--border-color-split) !important;
      color: var(--text-color) !important;
      background: var(--bg-card) !important;
    }

    .ant-table-tbody > tr:hover > td {
      background: var(--bg-hover) !important;
    }

    .ant-table-tbody > tr.ant-table-row-selected > td {
      background: var(--primary-color-light) !important;
    }

    .ant-table-pagination {
      .ant-pagination {
        .ant-pagination-item {
          background: var(--bg-card) !important;
          border-color: var(--border-color-base) !important;

          a {
            color: var(--text-color) !important;
          }

          &:hover {
            border-color: var(--primary-color) !important;

            a {
              color: var(--primary-color) !important;
            }
          }

          &.ant-pagination-item-active {
            background: var(--primary-color) !important;
            border-color: var(--primary-color) !important;

            a {
              color: var(--color-white) !important;
            }
          }
        }

        .ant-pagination-prev,
        .ant-pagination-next {
          .ant-pagination-item-link {
            background: var(--bg-card) !important;
            border-color: var(--border-color-base) !important;
            color: var(--text-color) !important;

            &:hover {
              border-color: var(--primary-color) !important;
              color: var(--primary-color) !important;
            }
          }
        }

        .ant-pagination-total-text {
          color: var(--text-color-secondary) !important;
        }
      }
    }
  }

  // Notification components dark mode enhancements - Professional, modern, smart and intuitive UI
  .notification-menu {
    background: var(--component-background) !important;
    border: 1px solid var(--border-color-base) !important;
    border-radius: 8px !important;
    box-shadow: var(--shadow-2) !important;

    .ant-menu-item-group-title {
      background: var(--component-background) !important;
      color: var(--text-color) !important;
      border-bottom: 1px solid var(--border-color-base) !important;
      font-weight: 600 !important;
    }

    .ant-menu-item {
      background: var(--component-background) !important;
      color: var(--text-color) !important;
      border-bottom: 1px solid var(--border-color-split) !important;
      transition: all 0.2s ease !important;

      &:hover {
        background: var(--item-hover-bg) !important;
      }

      &:last-child {
        border-bottom: none !important;
      }
    }

    .notification-unread {
      background: rgba(139, 109, 209, 0.08) !important;
      border-left: 3px solid var(--primary-color) !important;

      .message {
        color: var(--text-color) !important;
        font-weight: 600 !important;
      }
    }

    .notification-read {
      .message {
        color: var(--text-color-secondary) !important;
      }

      .time {
        color: var(--text-color-tertiary) !important;
      }
    }
  }

  // Avatar component in notifications
  .ant-avatar {
    border: 2px solid var(--border-color-base) !important;
    transition: all 0.2s ease !important;

    &:hover {
      border-color: var(--primary-color) !important;
    }
  }

  // Dropdown menu dark mode fixes for notifications
  .ant-dropdown-menu {
    background: var(--component-background) !important;
    border: 1px solid var(--border-color-base) !important;
    border-radius: 8px !important;
    box-shadow: var(--shadow-2) !important;

    .ant-dropdown-menu-item {
      color: var(--text-color) !important;
      transition: all 0.2s ease !important;

      &:hover {
        background: var(--item-hover-bg) !important;
      }
    }

    .ant-dropdown-menu-item-group-title {
      color: var(--text-color) !important;
      background: var(--component-background) !important;
      border-bottom: 1px solid var(--border-color-base) !important;
    }
  }

  // Select component dark mode fixes
  .ant-select {
    .ant-select-selector {
      background: var(--bg-card) !important;
      border-color: var(--border-color-base) !important;
      color: var(--text-color) !important;
      border-radius: 8px !important;

      .ant-select-selection-search-input {
        color: var(--text-color) !important;
      }

      .ant-select-selection-placeholder {
        color: var(--text-color-tertiary) !important;
      }

      .ant-select-selection-item {
        color: var(--text-color) !important;
      }
    }

    &:hover .ant-select-selector {
      border-color: var(--primary-color) !important;
    }

    &.ant-select-focused .ant-select-selector {
      border-color: var(--primary-color) !important;
      box-shadow: 0 0 0 2px var(--primary-color-light) !important;
    }

    .ant-select-arrow {
      color: var(--text-color-tertiary) !important;
    }

    .ant-select-clear {
      background: var(--bg-card) !important;
      color: var(--text-color-tertiary) !important;

      &:hover {
        color: var(--text-color) !important;
      }
    }
  }

  // Select dropdown dark mode fixes
  .ant-select-dropdown {
    background: var(--bg-modal) !important;
    border-radius: 8px !important;
    box-shadow: var(--shadow-3) !important;
    border: 1px solid var(--border-color-base) !important;

    .ant-select-item {
      color: var(--text-color) !important;

      &:hover {
        background: var(--bg-hover) !important;
      }

      &.ant-select-item-option-selected {
        background: var(--primary-color-light) !important;
        color: var(--primary-color) !important;
        font-weight: 600 !important;
      }

      &.ant-select-item-option-active {
        background: var(--bg-hover) !important;
      }
    }

    .ant-select-item-empty {
      color: var(--text-color-tertiary) !important;
    }
  }

  // Checkbox dark mode fixes for table row selection
  .ant-checkbox-wrapper {
    color: var(--text-color) !important;

    .ant-checkbox {
      .ant-checkbox-inner {
        background: var(--bg-card) !important;
        border-color: var(--border-color-base) !important;

        &:after {
          border-color: var(--color-white) !important;
        }
      }

      &:hover .ant-checkbox-inner {
        border-color: var(--primary-color) !important;
      }

      &.ant-checkbox-checked .ant-checkbox-inner {
        background: var(--primary-color) !important;
        border-color: var(--primary-color) !important;
      }

      &.ant-checkbox-indeterminate .ant-checkbox-inner {
        background: var(--primary-color) !important;
        border-color: var(--primary-color) !important;

        &:after {
          background: var(--color-white) !important;
        }
      }
    }
  }

  // Gallery stats
  .gal-stats {
    color: var(--text-color-secondary);
  }

  // Purchase and subscription blocks
  .purchase-block {
    background: var(--bg-overlay);
    color: var(--color-white);
  }

  .subscription-block {
    background: var(--bg-overlay);
    border-color: var(--border-color-base);
    color: var(--text-color);
  }

  // Verify info
  .verify-info {
    background-color: var(--error-color);
    color: var(--color-white);
  }

  // Login and Register components
  .login-box {
    background: var(--bg-white);

    .title {
      color: var(--primary-color);
    }

    a {
      color: var(--primary-color);

      &:hover {
        color: var(--primary-color);
        opacity: 0.8;
      }
    }

    .ant-checkbox-inner {
      border-color: var(--border-color-base);
      background-color: var(--component-background);

      &:after {
        border-color: var(--color-white);
      }
    }

    .ant-checkbox-checked .ant-checkbox-inner {
      background-color: var(--primary-color);
      border-color: var(--primary-color);
    }

    .btn-primary {
      background-image: var(--gradient-primary);
      color: var(--color-white);
      border: none;

      &:hover {
        background: var(--primary-color);
        transform: translateY(-1px);
        box-shadow: var(--shadow-2);
      }
    }

    // Switch buttons for register page
    .switchBtn button {
      color: var(--text-color-secondary);
      background: transparent;

      &:hover {
        color: var(--primary-color);
        background: var(--bg-hover);
      }

      &.active {
        color: var(--primary-color);
        background: var(--primary-color-light);
      }
    }

    // Welcome box for register page
    .welcomeBox {
      background: var(--card-background);
      border: 1px solid var(--border-color-base);
      box-shadow: var(--shadow-2);

      h3 {
        color: var(--text-color);
      }

      p {
        color: var(--text-color);
      }

      ul li {
        color: var(--text-color-secondary);

        &::before {
          color: var(--primary-color);
          background: var(--primary-color-light);
        }

        &:hover {
          color: var(--text-color);
        }
      }

      .sun-editor-editable {
        color: var(--text-color);

        p,
        div,
        span {
          color: var(--text-color) !important;
        }

        ul,
        ol {
          li {
            color: var(--text-color-secondary) !important;
          }
        }
      }
    }
  }

  // Teaser components
  .teaserButton {
    background: var(--bg-overlay);
    border-color: var(--border-color-base);
    color: var(--text-color);

    &:hover {
      background: var(--component-background);
    }
  }

  .teaserModal {
    .ant-modal-close {
      color: var(--color-white);
    }
  }

  // PPV badge
  .ppvBadge {
    background: var(--gradient-primary);
    color: var(--color-white);
  }

  // Schedule components
  .scheduleStatusBar {
    background-color: var(--bg-secondary);
    border-color: var(--border-color-split);

    .scheduleInfo {
      color: var(--text-color-secondary);

      .anticon {
        color: var(--theme-color);
      }
    }

    .viewScheduledBtn {
      color: var(--theme-color);

      &:hover {
        color: var(--primary-color);
      }
    }
  }

  // List items
  .ant-list-item {
    border-color: var(--border-color-split);

    .ant-list-item-meta-description {
      color: var(--text-color-secondary);
    }
  }

  // Options and actions
  .optionsIcon {
    color: var(--text-color-secondary);

    &:hover {
      color: var(--primary-color);
    }
  }

  // Image overlays and counters
  .imageCounter {
    background: var(--bg-overlay);
    color: var(--color-white);
  }

  .more-overlay {
    background: var(--bg-overlay);

    span {
      color: var(--color-white);
    }
  }

  .centerLock {
    background: var(--bg-overlay);
    color: var(--color-white);
  }

  .lockOverlay {
    background: var(--bg-overlay);
  }

  // Navigation buttons
  .navButton {
    background: var(--bg-overlay);
    border-color: var(--border-color-base);
    color: var(--text-color);

    &:hover {
      background: var(--primary-color);
      color: var(--color-white);
    }
  }

  // Earning Report Page Specific Dark Mode Fixes
  .statistic-earning {
    .ant-statistic {
      .ant-statistic-title {
        color: var(--text-color-secondary) !important;
        font-weight: 500;
      }

      .ant-statistic-content {
        color: var(--text-color) !important;
        font-weight: 600;

        .ant-statistic-content-value {
          color: var(--primary-color) !important;
        }

        .ant-statistic-content-prefix {
          color: var(--primary-color) !important;
        }
      }
    }
  }

  // Table responsive wrapper
  .table-responsive {
    .ant-table-wrapper {
      background: var(--component-background);
      border-radius: 8px;
      box-shadow: var(--shadow-1);
      overflow: hidden;
    }

    .ant-table {
      background: var(--component-background) !important;
      color: var(--text-color) !important;

      .ant-table-thead > tr > th {
        background: var(--bg-secondary) !important;
        color: var(--text-color) !important;
        border-bottom: 1px solid var(--border-color-base) !important;
        font-weight: 600;
        padding: 16px;

        &.ant-table-column-sort {
          background: var(--bg-secondary) !important;
        }

        .ant-table-column-sorter {
          color: var(--text-color-tertiary) !important;

          &.ant-table-column-sorter-up.active,
          &.ant-table-column-sorter-down.active {
            color: var(--primary-color) !important;
          }
        }
      }

      .ant-table-tbody > tr {
        background: var(--component-background) !important;

        > td {
          background: var(--component-background) !important;
          color: var(--text-color) !important;
          border-bottom: 1px solid var(--border-color-split) !important;
        }

        &:hover > td {
          background: var(--item-hover-bg) !important;
        }

        &.ant-table-row-selected > td {
          background: var(--primary-color-light) !important;
        }
      }

      .ant-table-pagination {
        background: var(--component-background) !important;
        border-top: 1px solid var(--border-color-base) !important;
        padding: 16px;

        .ant-pagination {
          .ant-pagination-item {
            background: var(--component-background) !important;
            border-color: var(--border-color-base) !important;

            a {
              color: var(--text-color) !important;
            }

            &:hover {
              border-color: var(--primary-color) !important;

              a {
                color: var(--primary-color) !important;
              }
            }

            &.ant-pagination-item-active {
              background: var(--primary-color) !important;
              border-color: var(--primary-color) !important;

              a {
                color: var(--color-white) !important;
              }
            }
          }

          .ant-pagination-prev,
          .ant-pagination-next {
            background: var(--component-background) !important;
            border-color: var(--border-color-base) !important;

            .ant-pagination-item-link {
              background: var(--component-background) !important;
              border-color: var(--border-color-base) !important;
              color: var(--text-color) !important;
              transition: all 0.3s ease !important;
            }

            &:hover {
              border-color: var(--primary-color) !important;

              .ant-pagination-item-link {
                background: var(--component-background) !important;
                border-color: var(--primary-color) !important;
                color: var(--primary-color) !important;
              }
            }

            &.ant-pagination-disabled {
              .ant-pagination-item-link {
                background: var(--bg-disabled) !important;
                border-color: var(--border-color-base) !important;
                color: var(--text-color-disabled) !important;
                cursor: not-allowed !important;
              }
            }
          }

          // Additional overrides for hardcoded pagination item link colors
          .ant-pagination-prev .ant-pagination-item-link,
          .ant-pagination-next .ant-pagination-item-link {
            background-color: var(--component-background) !important;
            border: 1px solid var(--border-color-base) !important;
            color: var(--text-color) !important;
            transition: all 0.3s ease !important;

            &:hover {
              background-color: var(--component-background) !important;
              border-color: var(--primary-color) !important;
              color: var(--primary-color) !important;
            }
          }

          // Disabled state overrides
          .ant-pagination-disabled {
            .ant-pagination-item-link {
              background-color: var(--bg-disabled) !important;
              border-color: var(--border-color-base) !important;
              color: var(--text-color-disabled) !important;
              cursor: not-allowed !important;
            }
          }
        }
      }
    }
  }

  // Tag components with better dark mode colors
  .ant-tag {
    background: var(--bg-secondary) !important;
    color: var(--text-color) !important;
    border-color: var(--border-color-base) !important;
    border-radius: 4px;
    font-weight: 500;

    // Status-based tag colors for dark mode
    &.ant-tag-red,
    &[color='red'] {
      background: rgba(255, 82, 82, 0.15) !important;
      color: #ff6b6b !important;
      border-color: rgba(255, 82, 82, 0.3) !important;
    }

    &.ant-tag-blue,
    &[color='blue'] {
      background: rgba(24, 144, 255, 0.15) !important;
      color: #4dabf7 !important;
      border-color: rgba(24, 144, 255, 0.3) !important;
    }

    &.ant-tag-green,
    &[color='green'] {
      background: rgba(16, 209, 58, 0.15) !important;
      color: #51cf66 !important;
      border-color: rgba(16, 209, 58, 0.3) !important;
    }

    &.ant-tag-yellow,
    &[color='yellow'] {
      background: rgba(255, 193, 7, 0.15) !important;
      color: #ffd43b !important;
      border-color: rgba(255, 193, 7, 0.3) !important;
    }

    &.ant-tag-orange,
    &[color='orange'] {
      background: rgba(255, 146, 51, 0.15) !important;
      color: #ff922b !important;
      border-color: rgba(255, 146, 51, 0.3) !important;
    }

    &.ant-tag-cyan,
    &[color='cyan'] {
      background: rgba(34, 139, 230, 0.15) !important;
      color: #22b8cf !important;
      border-color: rgba(34, 139, 230, 0.3) !important;
    }

    &.ant-tag-magenta,
    &[color='magenta'] {
      background: rgba(214, 51, 132, 0.15) !important;
      color: #f06292 !important;
      border-color: rgba(214, 51, 132, 0.3) !important;
    }

    &.ant-tag-gold,
    &[color='gold'] {
      background: rgba(255, 193, 7, 0.15) !important;
      color: #ffd43b !important;
      border-color: rgba(255, 193, 7, 0.3) !important;
    }

    // Semantic tag colors
    &.ant-tag-success,
    &[color='success'] {
      background: rgba(16, 209, 58, 0.15) !important;
      color: #51cf66 !important;
      border-color: rgba(16, 209, 58, 0.3) !important;
    }

    &.ant-tag-warning,
    &[color='warning'] {
      background: rgba(255, 193, 7, 0.15) !important;
      color: #ffd43b !important;
      border-color: rgba(255, 193, 7, 0.3) !important;
    }

    &.ant-tag-danger,
    &[color='danger'] {
      background: rgba(255, 82, 82, 0.15) !important;
      color: #ff6b6b !important;
      border-color: rgba(255, 82, 82, 0.3) !important;
    }

    // Custom hex color tags
    &[color='#FFCF00'] {
      background: rgba(255, 207, 0, 0.15) !important;
      color: #ffd43b !important;
      border-color: rgba(255, 207, 0, 0.3) !important;
    }

    &[color='#936dc9'] {
      background: rgba(147, 109, 201, 0.15) !important;
      color: #a47dd9 !important;
      border-color: rgba(147, 109, 201, 0.3) !important;
    }
  }

  // Global heading elements dark mode fixes - Professional, modern, smart and intuitive UI
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    color: var(--text-color) !important;
    transition: color 0.3s ease !important;
  }

  // Layout components
  .ant-layout {
    background: var(--bg-white);
  }

  .ant-layout-header {
    background: var(--component-background);
    border-bottom: 1px solid var(--border-color-base);
    color: var(--text-color);
  }

  .ant-layout-content {
    background: var(--bg-white);
  }

  .ant-layout-sider {
    background: var(--component-background);
  }

  // Card components
  .ant-card {
    background: var(--card-background);
    border-color: var(--border-color-base);
    color: var(--text-color);
    box-shadow: var(--shadow-1);

    .ant-card-head {
      background: var(--card-background);
      border-bottom-color: var(--border-color-base);
      color: var(--text-color);
    }

    .ant-card-body {
      background: var(--card-background);
      color: var(--text-color);
    }
  }

  // Button components
  .ant-btn {
    border-color: var(--border-color-base);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    &:not(.ant-btn-primary) {
      background: var(--button-background);
      color: var(--text-color);

      &:hover {
        background: var(--item-hover-bg);
        border-color: var(--primary-color);
        color: var(--primary-color);
        box-shadow: var(--shadow-2);
      }

      &:focus {
        background: var(--item-hover-bg);
        border-color: var(--primary-color);
        color: var(--primary-color);
        box-shadow: 0 0 0 2px var(--primary-color-light);
      }
    }

    &.ant-btn-primary {
      background: var(--gradient-primary);
      border-color: var(--primary-color);
      box-shadow: var(--shadow-1);

      &:hover {
        transform: translateY(-1px);
        box-shadow: var(--shadow-2);
      }
    }
  }

  .ant-input {
    background: var(--component-background);
    border-color: var(--border-color-base);
    color: var(--text-color);

    &:hover {
      border-color: var(--primary-color);
    }

    &:focus {
      border-color: var(--primary-color);
      box-shadow: 0 0 0 2px var(--primary-color-fade);
    }
  }

  .ant-select {
    .ant-select-selector {
      background: var(--component-background);
      border-color: var(--border-color-base);
      color: var(--text-color);
    }
  }

  .ant-dropdown {
    background: var(--component-background);
    border: 1px solid var(--border-color-base);
  }

  .ant-menu {
    background: var(--component-background);
    color: var(--text-color);

    .ant-menu-item {
      color: var(--text-color);

      &:hover {
        background: var(--item-hover-bg);
        color: var(--primary-color);
      }

      &.ant-menu-item-selected {
        background: var(--item-active-bg);
        color: var(--primary-color);
      }
    }
  }

  .ant-modal {
    .ant-modal-content {
      background: var(--component-background);
    }

    .ant-modal-header {
      background: var(--component-background);
      border-bottom: 1px solid var(--border-color-base);
    }

    .ant-modal-title {
      color: var(--text-color);
    }

    .ant-modal-body {
      color: var(--text-color);
    }
  }

  .ant-table {
    background: var(--component-background);

    .ant-table-thead > tr > th {
      background: var(--bg-secondary);
      border-bottom: 1px solid var(--border-color-base);
      color: var(--text-color);

      // Fix for table header column separator dark mode
      &:not(:last-child):not(.ant-table-selection-column):not(
          .ant-table-row-expand-icon-cell
        ):not([colspan])::before {
        background-color: var(--border-color-split) !important;
      }
    }

    .ant-table-tbody > tr > td {
      border-bottom: 1px solid var(--border-color-split);
      color: var(--text-color);
    }

    .ant-table-tbody > tr:hover > td {
      background: var(--item-hover-bg);
    }
  }

  // Drawer components
  .ant-drawer {
    .ant-drawer-content {
      background: var(--bg-drawer);
    }

    .ant-drawer-header {
      background: var(--bg-drawer);
      border-bottom: 1px solid var(--border-color-base);

      .ant-drawer-title {
        color: var(--text-color);
      }
    }

    .ant-drawer-body {
      background: var(--bg-drawer);
      color: var(--text-color);
    }

    .ant-drawer-close {
      color: var(--text-color-secondary);

      &:hover {
        color: var(--primary-color);
      }
    }
  }

  .ant-drawer-mask {
    background: var(--bg-overlay);
  }

  // Form components
  .ant-form-item-label > label {
    color: var(--text-color);
  }

  .ant-form-item-explain-error {
    color: var(--error-color);
  }

  // Divider components
  .ant-divider {
    border-color: var(--border-color-base);

    // Divider with text content
    &.ant-divider-with-text {
      color: var(--text-color);

      &::before,
      &::after {
        border-top-color: var(--border-color-base);
      }

      .ant-divider-inner-text {
        color: var(--text-color-secondary);
        background-color: var(--component-background);
        padding: 0 16px;
        font-weight: 500;
        border-radius: 4px;
        transition: all 0.3s ease;
      }
    }

    // Horizontal divider
    &.ant-divider-horizontal {
      border-top-color: var(--border-color-base);

      &.ant-divider-with-text-left,
      &.ant-divider-with-text-right,
      &.ant-divider-with-text-center {
        .ant-divider-inner-text {
          color: var(--text-color-secondary);
          background-color: var(--component-background);
        }
      }
    }

    // Vertical divider
    &.ant-divider-vertical {
      border-left-color: var(--border-color-base);
    }

    // Dashed divider
    &.ant-divider-dashed {
      border-color: var(--border-color-base);
      border-style: dashed;
    }
  }

  // Tooltip
  .ant-tooltip {
    .ant-tooltip-inner {
      background: var(--bg-modal);
      color: var(--text-color);
      box-shadow: var(--shadow-2);
      border-radius: 6px;
    }

    .ant-tooltip-arrow::before {
      background: var(--bg-modal);
      border-color: var(--bg-modal);
    }

    .ant-tooltip-arrow::after {
      background: var(--bg-modal);
      border-color: var(--bg-modal);
    }
  }

  // Popover
  .ant-popover {
    .ant-popover-inner {
      background: var(--bg-modal);
      box-shadow: var(--shadow-3);
      border: 1px solid var(--border-color-base);
      border-radius: 8px;
    }

    .ant-popover-title {
      background: var(--bg-modal);
      border-bottom: 1px solid var(--border-color-base);
      color: var(--text-color);
    }

    .ant-popover-inner-content {
      background: var(--bg-modal);
      color: var(--text-color);
    }

    .ant-popover-arrow::before {
      background: var(--bg-modal);
      border-color: var(--border-color-base);
    }

    .ant-popover-arrow::after {
      background: var(--bg-modal);
      border-color: var(--bg-modal);
    }
  }

  // Dropdown menu
  .ant-dropdown-menu {
    background: var(--bg-modal);
    border: 1px solid var(--border-color-base);
    box-shadow: var(--shadow-3);
    border-radius: 8px;

    .ant-dropdown-menu-item {
      color: var(--text-color);
      transition: all 0.3s ease;

      &:hover {
        background: var(--item-hover-bg);
        color: var(--primary-color);
      }

      &:first-child {
        border-top-left-radius: 8px;
        border-top-right-radius: 8px;
      }

      &:last-child {
        border-bottom-left-radius: 8px;
        border-bottom-right-radius: 8px;
      }
    }

    .ant-dropdown-menu-item-divider {
      background-color: var(--border-color-base);
    }
  }

  // Select dropdown
  .ant-select-dropdown {
    background: var(--bg-modal);
    border: 1px solid var(--border-color-base);
    box-shadow: var(--shadow-3);

    .ant-select-item {
      color: var(--text-color);

      &:hover {
        background: var(--item-hover-bg);
      }

      &.ant-select-item-option-selected {
        background: var(--primary-color-light);
        color: var(--primary-color);
      }
    }
  }

  // Input improvements
  .ant-input {
    &::placeholder {
      color: var(--text-color-tertiary);
    }

    // Fix autofill styling in dark mode
    &:-webkit-autofill,
    &:-webkit-autofill:hover,
    &:-webkit-autofill:focus,
    &:-webkit-autofill:active {
      -webkit-box-shadow: 0 0 0 30px var(--component-background) inset !important;
      -webkit-text-fill-color: var(--text-color) !important;
      background-color: var(--component-background) !important;
      background-image: none !important;
      transition: background-color 5000s ease-in-out 0s;
    }

    // Fix for newer browsers with light-dark() function
    &:-internal-autofill-selected {
      background-color: var(--component-background) !important;
      background-image: none !important;
      color: var(--text-color) !important;
    }
  }

  .ant-input-affix-wrapper {
    background: var(--input-background);
    border-color: var(--border-color-base);

    &:hover {
      border-color: var(--primary-color);
    }

    &.ant-input-affix-wrapper-focused {
      border-color: var(--primary-color);
      box-shadow: 0 0 0 2px var(--primary-color-light);
    }

    .ant-input {
      background: transparent;
      color: var(--text-color);
    }

    // Fix error state styling in dark mode
    &.ant-input-affix-wrapper-status-error {
      background: var(--component-background) !important;
      border-color: var(--error-color) !important;

      &:hover {
        background: var(--component-background) !important;
        border-color: var(--error-color) !important;
      }

      &:focus,
      &.ant-input-affix-wrapper-focused {
        background: var(--component-background) !important;
        border-color: var(--error-color) !important;
        box-shadow: 0 0 0 2px rgba(240, 65, 52, 0.2) !important;
      }
    }
  }

  // Input number affix wrapper dark mode fix
  .ant-input-number-affix-wrapper {
    background: var(--input-background) !important;
    border-color: var(--border-color-base) !important;
    color: var(--text-color) !important;
    transition: all 0.3s ease !important;

    &:hover {
      border-color: var(--primary-color) !important;
    }

    &:focus-within,
    &.ant-input-number-affix-wrapper-focused {
      border-color: var(--primary-color) !important;
      box-shadow: 0 0 0 2px var(--primary-color-fade) !important;
    }

    .ant-input-number-input {
      background: transparent !important;
      color: var(--text-color) !important;

      &::placeholder {
        color: var(--text-color-tertiary) !important;
      }
    }

    .ant-input-number-handler-wrap {
      background: var(--input-background) !important;
      border-left-color: var(--border-color-base) !important;

      .ant-input-number-handler {
        color: var(--text-color-secondary) !important;
        border-color: var(--border-color-base) !important;

        &:hover {
          background: var(--item-hover-bg) !important;
          color: var(--primary-color) !important;
        }
      }
    }
  }

  // Select improvements
  .ant-select {
    &.ant-select-focused .ant-select-selector {
      border-color: var(--primary-color);
      box-shadow: 0 0 0 2px var(--primary-color-light);
    }

    .ant-select-selector {
      background: var(--component-background);
      border-color: var(--border-color-base);
      color: var(--text-color);

      .ant-select-selection-search-input {
        color: var(--text-color);
      }

      .ant-select-selection-item {
        color: var(--text-color) !important;
      }
    }

    .ant-select-selection-placeholder {
      color: var(--text-color-tertiary);
    }

    // Fix dropdown arrow visibility
    .ant-select-arrow {
      color: var(--text-color-secondary) !important;

      .anticon {
        color: var(--text-color-secondary) !important;
      }
    }
  }

  // Additional specificity for select selection items to ensure dark mode text visibility
  .ant-select .ant-select-selector .ant-select-selection-item {
    color: var(--text-color) !important;
  }

  // Fix for multiple select selection items with hardcoded colors
  .ant-select-multiple .ant-select-selection-item {
    background: var(--bg-secondary) !important;
    border: 1px solid var(--border-color-base) !important;
    color: var(--text-color) !important;

    // Close button styling for selected items
    .ant-select-selection-item-remove {
      color: var(--text-color-secondary) !important;

      &:hover {
        color: var(--error-color) !important;
        background: rgba(255, 82, 82, 0.1) !important;
      }
    }
  }

  .ant-select {
    &:hover .ant-select-arrow {
      color: var(--text-color) !important;

      .anticon {
        color: var(--text-color) !important;
      }
    }

    // Error state for select
    &.ant-select-status-error {
      .ant-select-selector {
        background: var(--component-background) !important;
        border-color: var(--error-color) !important;
      }

      &:hover .ant-select-selector {
        background: var(--component-background) !important;
        border-color: var(--error-color) !important;
      }

      &.ant-select-focused .ant-select-selector {
        background: var(--component-background) !important;
        border-color: var(--error-color) !important;
        box-shadow: 0 0 0 2px rgba(240, 65, 52, 0.2) !important;
      }
    }
  }

  // Select dropdown
  .ant-select-dropdown {
    background: var(--bg-modal);
    border: 1px solid var(--border-color-base);
    box-shadow: var(--shadow-3);

    .ant-select-item {
      color: var(--text-color);

      &:hover {
        background: var(--item-hover-bg);
      }

      &.ant-select-item-option-selected {
        background: var(--primary-color);
        color: var(--color-white);
      }

      &.ant-select-item-option-active {
        background: var(--item-hover-bg);
      }
    }

    .ant-empty {
      color: var(--text-color-secondary);
    }
  }

  // Modal improvements
  .ant-modal-mask {
    background: var(--bg-overlay);
  }

  .ant-modal {
    .ant-modal-close {
      color: var(--text-color-secondary);

      &:hover {
        color: var(--primary-color);
      }
    }

    .ant-modal-footer {
      background: var(--bg-modal);
      border-top: 1px solid var(--border-color-base);
    }
  }

  // DatePicker improvements
  .ant-picker {
    background: var(--component-background);
    border-color: var(--border-color-base);
    color: var(--text-color);

    &:hover {
      border-color: var(--primary-color);
    }

    &.ant-picker-focused {
      border-color: var(--primary-color);
      box-shadow: 0 0 0 2px var(--primary-color-light);
    }

    .ant-picker-input > input {
      color: var(--text-color);

      &::placeholder {
        color: var(--text-color-tertiary);
      }
    }

    .ant-picker-suffix {
      color: var(--text-color-secondary);
    }

    // DatePicker separator icon dark mode fix - Professional, modern, smart and intuitive UI
    .ant-picker-separator {
      .anticon {
        color: var(--text-color-secondary) !important;
        transition: color 0.3s ease !important;

        &:hover {
          color: var(--primary-color) !important;
        }
      }
    }
  }

  // DatePicker dropdown
  .ant-picker-dropdown {
    .ant-picker-panel-container {
      background: var(--bg-modal);
      border: 1px solid var(--border-color-base);
      box-shadow: var(--shadow-3);
    }

    .ant-picker-panel {
      background: var(--bg-modal);
      border: none;

      .ant-picker-header {
        color: var(--text-color) !important;
        border-bottom: 1px solid var(--border-color-base);

        button {
          color: var(--text-color);

          &:hover {
            color: var(--primary-color);
          }
        }
      }

      .ant-picker-content {
        th {
          color: var(--text-color-secondary);
        }

        .ant-picker-cell {
          color: var(--text-color);

          &:hover .ant-picker-cell-inner {
            background: var(--item-hover-bg);
          }

          &.ant-picker-cell-selected .ant-picker-cell-inner {
            background: var(--primary-color);
            color: var(--color-white);
          }

          &.ant-picker-cell-today .ant-picker-cell-inner {
            border-color: var(--primary-color);
          }
        }
      }
    }

    // Fix for complex picker cell hover states with hardcoded #f5f5f5 background
    .ant-picker-cell:hover:not(.ant-picker-cell-in-view) .ant-picker-cell-inner,
    .ant-picker-cell:hover:not(.ant-picker-cell-selected):not(
        .ant-picker-cell-range-start
      ):not(.ant-picker-cell-range-end):not(
        .ant-picker-cell-range-hover-start
      ):not(.ant-picker-cell-range-hover-end)
      .ant-picker-cell-inner {
      background: var(--item-hover-bg) !important;
    }

    // Additional picker cell states for comprehensive dark mode support
    .ant-picker-dropdown {
      .ant-picker-cell {
        &:not(.ant-picker-cell-in-view) {
          .ant-picker-cell-inner {
            color: var(--text-color-disabled);
          }

          &:hover .ant-picker-cell-inner {
            background: var(--item-hover-bg) !important;
            color: var(--text-color-secondary);
          }
        }

        &.ant-picker-cell-range-start .ant-picker-cell-inner,
        &.ant-picker-cell-range-end .ant-picker-cell-inner {
          background: var(--primary-color);
          color: var(--color-white);
        }

        &.ant-picker-cell-range-hover-start .ant-picker-cell-inner,
        &.ant-picker-cell-range-hover-end .ant-picker-cell-inner {
          background: var(--primary-color-light);
          color: var(--primary-color);
        }

        &.ant-picker-cell-in-range .ant-picker-cell-inner {
          background: var(--primary-color-light);
          color: var(--text-color);
        }
      }
    }

    // Time picker panel dark mode fixes - Professional, modern, smart and intuitive UI
    .ant-picker-time-panel {
      background: var(--bg-modal);
      border-color: var(--border-color-base);

      .ant-picker-time-panel-column {
        > li {
          color: var(--text-color);

          &:hover {
            background: var(--item-hover-bg);
          }

          &.ant-picker-time-panel-cell-selected {
            .ant-picker-time-panel-cell-inner {
              background: var(--primary-color-light) !important;
              color: var(--primary-color) !important;
              transition: all 0.3s ease !important;
            }
          }

          &.ant-picker-time-panel-cell-disabled {
            color: var(--text-color-disabled);
            cursor: not-allowed;

            &:hover {
              background: transparent;
            }
          }
        }
      }
    }

    // Fix for hardcoded #f5f5f5 background in time picker cell inner hover state
    .ant-picker-time-panel-column
      > li.ant-picker-time-panel-cell
      .ant-picker-time-panel-cell-inner:hover {
      background: var(--item-hover-bg) !important;
      color: var(--primary-color) !important;
    }

    // Calendar component dark mode fixes - Professional, modern, smart and intuitive UI
    .ant-picker-calendar {
      background: var(--component-background) !important;
      color: var(--text-color) !important;

      .ant-picker-panel {
        background: var(--component-background) !important;
        border: 0 !important;
        border-top: 1px solid var(--border-color-split) !important;
        border-radius: 0 !important;
        color: var(--text-color) !important;
      }

      .ant-picker-calendar-header {
        background: var(--component-background) !important;
        border-bottom: 1px solid var(--border-color-split) !important;
        color: var(--text-color) !important;

        .ant-picker-calendar-year-select,
        .ant-picker-calendar-month-select {
          color: var(--text-color) !important;
        }
      }

      .ant-picker-content {
        background: var(--component-background) !important;

        th {
          color: var(--text-color-secondary) !important;
          background: var(--bg-secondary) !important;
        }

        .ant-picker-cell {
          color: var(--text-color) !important;

          &:hover {
            background: var(--item-hover-bg) !important;
          }

          &.ant-picker-cell-selected {
            background: var(--primary-color-light) !important;

            .ant-picker-cell-inner {
              background: var(--primary-color) !important;
              color: var(--color-white) !important;
            }
          }

          &.ant-picker-cell-today {
            .ant-picker-cell-inner {
              border-color: var(--primary-color) !important;
              color: var(--primary-color) !important;
            }
          }

          &.ant-picker-cell-in-view {
            color: var(--text-color) !important;
          }

          &:not(.ant-picker-cell-in-view) {
            color: var(--text-color-disabled) !important;
          }
        }
      }

      .ant-picker-calendar-date {
        background: var(--component-background) !important;
        border-color: var(--border-color-split) !important;
        color: var(--text-color) !important;

        &:hover {
          background: var(--item-hover-bg) !important;
        }
      }

      .ant-picker-calendar-date-value {
        color: var(--text-color) !important;
      }

      .ant-picker-calendar-date-content {
        color: var(--text-color-secondary) !important;
      }
    }
  }

  // Message component improvements
  .ant-message {
    .ant-message-notice-content {
      background: var(--bg-modal);
      color: var(--text-color);
      box-shadow: var(--shadow-3);
    }
  }

  // Notification improvements
  .ant-notification {
    .ant-notification-notice {
      background: var(--bg-modal);
      border: 1px solid var(--border-color-base);
      color: var(--text-color);
      box-shadow: var(--shadow-3);

      .ant-notification-notice-message {
        color: var(--text-color);
      }

      .ant-notification-notice-description {
        color: var(--text-color-secondary);
      }

      .ant-notification-notice-close {
        color: var(--text-color-secondary);

        &:hover {
          color: var(--primary-color);
        }
      }
    }
  }

  // Global autofill fixes for all input types
  input:-webkit-autofill,
  input:-webkit-autofill:hover,
  input:-webkit-autofill:focus,
  input:-webkit-autofill:active,
  .ant-input:-webkit-autofill,
  .ant-input:-webkit-autofill:hover,
  .ant-input:-webkit-autofill:focus,
  .ant-input:-webkit-autofill:active {
    -webkit-box-shadow: 0 0 0 30px var(--component-background) inset !important;
    -webkit-text-fill-color: var(--text-color) !important;
    background-color: var(--component-background) !important;
    background-image: none !important;
    transition: background-color 5000s ease-in-out 0s;
  }

  // Fix for newer browsers with light-dark() function and internal autofill
  input:-internal-autofill-selected,
  .ant-input:-internal-autofill-selected {
    background-color: var(--component-background) !important;
    background-image: none !important;
    color: var(--text-color) !important;
    appearance: none !important;
  }

  // Additional fix for form inputs specifically
  form input:-webkit-autofill,
  form input:-webkit-autofill:hover,
  form input:-webkit-autofill:focus,
  form input:-webkit-autofill:active,
  form .ant-input:-webkit-autofill,
  form .ant-input:-webkit-autofill:hover,
  form .ant-input:-webkit-autofill:focus,
  form .ant-input:-webkit-autofill:active {
    -webkit-box-shadow: 0 0 0 30px var(--component-background) inset !important;
    -webkit-text-fill-color: var(--text-color) !important;
    background-color: var(--component-background) !important;
    background-image: none !important;
    border-color: var(--border-color-base) !important;
  }

  form input:-internal-autofill-selected,
  form .ant-input:-internal-autofill-selected {
    background-color: var(--component-background) !important;
    background-image: none !important;
    color: var(--text-color) !important;
    appearance: none !important;
    border-color: var(--border-color-base) !important;
  }

  // Fix autofill for input wrappers
  .ant-input-affix-wrapper input:-webkit-autofill,
  .ant-input-affix-wrapper input:-webkit-autofill:hover,
  .ant-input-affix-wrapper input:-webkit-autofill:focus,
  .ant-input-affix-wrapper input:-webkit-autofill:active {
    -webkit-box-shadow: 0 0 0 30px var(--component-background) inset !important;
    -webkit-text-fill-color: var(--text-color) !important;
    background-color: var(--component-background) !important;
    background-image: none !important;
  }

  .ant-input-affix-wrapper input:-internal-autofill-selected {
    background-color: var(--component-background) !important;
    background-image: none !important;
    color: var(--text-color) !important;
    appearance: none !important;
  }

  // Global Ant Design component error state fixes
  .ant-input-status-error,
  .ant-input-affix-wrapper-status-error,
  .ant-select-status-error .ant-select-selector,
  .ant-picker-status-error {
    background: var(--component-background) !important;
    border-color: var(--error-color) !important;

    &:hover {
      background: var(--component-background) !important;
      border-color: var(--error-color) !important;
    }

    &:focus,
    &.ant-input-affix-wrapper-focused,
    &.ant-select-focused,
    &.ant-picker-focused {
      background: var(--component-background) !important;
      border-color: var(--error-color) !important;
      box-shadow: 0 0 0 2px rgba(240, 65, 52, 0.2) !important;
    }
  }

  // Global form component improvements
  .ant-form-item-label > label {
    color: var(--text-color);
  }

  // Result component dark mode fixes - Professional, modern, smart and intuitive UI
  .ant-result {
    background: transparent !important;
    color: var(--text-color) !important;

    // Result title styling
    .ant-result-title {
      color: var(--text-color) !important;
      font-weight: 600 !important;
    }

    // Result subtitle styling
    .ant-result-subtitle {
      color: var(--text-color-secondary) !important;
      font-size: 16px !important;
      line-height: 1.6 !important;
      margin-bottom: 24px !important;
    }

    // Result icon styling for different statuses
    .ant-result-icon {
      margin-bottom: 24px !important;

      // Error status icon
      &.ant-result-error .ant-result-icon-error {
        color: var(--error-color) !important;

        .ant-result-error-icon {
          color: var(--error-color) !important;
        }
      }

      // Success status icon
      &.ant-result-success .ant-result-icon-success {
        color: var(--success-color) !important;
      }

      // Warning status icon
      &.ant-result-warning .ant-result-icon-warning {
        color: var(--warning-color) !important;
      }

      // Info status icon
      &.ant-result-info .ant-result-icon-info {
        color: var(--info-color) !important;
      }

      // 404 status icon
      &.ant-result-404 .ant-result-icon-404 {
        color: var(--text-color-tertiary) !important;
      }

      // 403 status icon
      &.ant-result-403 .ant-result-icon-403 {
        color: var(--text-color-tertiary) !important;
      }

      // 500 status icon
      &.ant-result-500 .ant-result-icon-500 {
        color: var(--text-color-tertiary) !important;
      }
    }

    // Result extra content (buttons area)
    .ant-result-extra {
      margin-top: 32px !important;

      .ant-btn {
        margin: 0 8px !important;
        border-radius: 6px !important;
        font-weight: 500 !important;
        transition: all 0.3s ease !important;
        box-shadow: none !important;
        height: 40px !important;
        padding: 0 24px !important;
        font-size: 14px !important;

        // Primary button styling
        &.primary {
          background: var(--primary-gradient) !important;
          border: none !important;
          color: var(--color-white) !important;

          &:hover {
            background: var(--primary-gradient-hover) !important;
            transform: translateY(-1px) !important;
            box-shadow: var(--shadow-2) !important;
          }

          &:active {
            transform: translateY(0) !important;
          }
        }

        // Secondary button styling
        &.secondary {
          background: var(--bg-card) !important;
          border: 1px solid var(--border-color-base) !important;
          color: var(--text-color) !important;

          &:hover {
            background: var(--bg-hover) !important;
            border-color: var(--primary-color) !important;
            color: var(--primary-color) !important;
            transform: translateY(-1px) !important;
            box-shadow: var(--shadow-1) !important;
          }

          &:active {
            transform: translateY(0) !important;
          }
        }

        // Icon styling within buttons
        .anticon {
          margin-right: 8px !important;
          font-size: 16px !important;
        }
      }
    }

    // Content area styling
    .ant-result-content {
      background: transparent !important;
      color: var(--text-color) !important;
    }
  }

  // Fix for select clear button dark mode
  .ant-select-clear {
    color: var(--text-color-tertiary) !important;
    background: var(--component-background) !important;
    transition: color 0.3s ease, opacity 0.15s ease;

    &:hover {
      color: var(--text-color-secondary) !important;
      opacity: 1 !important;
    }
  }

  // Fix for TextArea show count dark mode
  .ant-input-textarea-show-count::after {
    color: var(--text-color-tertiary) !important;
  }

  // Additional Skeleton component fixes for comprehensive coverage
  .ant-skeleton {
    .ant-skeleton-content {
      .ant-skeleton-title {
        background: linear-gradient(
          90deg,
          var(--bg-disabled) 25%,
          var(--bg-hover) 37%,
          var(--bg-disabled) 63%
        );
        background-size: 400% 100%;
        animation: skeleton-loading 1.4s ease infinite;
      }

      .ant-skeleton-paragraph > li {
        background: linear-gradient(
          90deg,
          var(--bg-disabled) 25%,
          var(--bg-hover) 37%,
          var(--bg-disabled) 63%
        );
        background-size: 400% 100%;
        animation: skeleton-loading 1.4s ease infinite;
      }
    }

    .ant-skeleton-header .ant-skeleton-avatar {
      background: linear-gradient(
        90deg,
        var(--bg-disabled) 25%,
        var(--bg-hover) 37%,
        var(--bg-disabled) 63%
      );
      background-size: 400% 100%;
      animation: skeleton-loading 1.4s ease infinite;
    }

    .ant-skeleton-button {
      background: linear-gradient(
        90deg,
        var(--bg-disabled) 25%,
        var(--bg-hover) 37%,
        var(--bg-disabled) 63%
      );
      background-size: 400% 100%;
      animation: skeleton-loading 1.4s ease infinite;
    }

    .ant-skeleton-input {
      background: linear-gradient(
        90deg,
        var(--bg-disabled) 25%,
        var(--bg-hover) 37%,
        var(--bg-disabled) 63%
      );
      background-size: 400% 100%;
      animation: skeleton-loading 1.4s ease infinite;
    }

    .ant-skeleton-image {
      background: var(--bg-disabled);

      .ant-skeleton-image-svg {
        color: var(--text-color-disabled);
        opacity: 0.3;
      }
    }
  }

  // Spin component comprehensive fixes
  .ant-spin {
    color: var(--primary-color);

    .ant-spin-dot .ant-spin-dot-item {
      background-color: var(--primary-color);
    }

    .ant-spin-text {
      color: var(--text-color);
    }
  }

  // Loading overlay fixes
  .ant-spin-container {
    background: var(--component-background);
  }

  .ant-spin-nested-loading > div > .ant-spin {
    background: var(--bg-overlay);
  }

  .ant-form-item-explain-error {
    color: var(--error-color);
  }

  .ant-form-item-explain-success {
    color: var(--success-color);
  }

  // Global button improvements
  .ant-btn {
    &.ant-btn-default {
      background: var(--component-background);
      border-color: var(--border-color-base);
      color: var(--text-color);

      &:hover {
        background: var(--item-hover-bg);
        border-color: var(--primary-color);
        color: var(--primary-color);
      }
    }

    &.ant-btn-ghost {
      background: transparent;
      border-color: var(--border-color-base);
      color: var(--text-color);

      &:hover {
        background: var(--item-hover-bg);
        border-color: var(--primary-color);
        color: var(--primary-color);
      }
    }
  }

  // Global checkbox improvements
  .ant-checkbox-wrapper {
    color: var(--text-color);

    .ant-checkbox {
      .ant-checkbox-inner {
        background: var(--component-background);
        border-color: var(--border-color-base);
      }

      &.ant-checkbox-checked .ant-checkbox-inner {
        background: var(--primary-color);
        border-color: var(--primary-color);
      }
    }
  }

  // Global radio improvements
  .ant-radio-wrapper {
    color: var(--text-color);

    .ant-radio {
      .ant-radio-inner {
        background: var(--component-background);
        border-color: var(--border-color-base);
      }

      &.ant-radio-checked .ant-radio-inner {
        border-color: var(--primary-color);

        &::after {
          background: var(--primary-color);
        }
      }
    }
  }

  // Container and layout improvements
  .container {
    background: var(--bg-white);
    min-height: 100vh;
  }

  // Page-specific improvements for auth pages
  .auth-page {
    background: var(--bg-white);
    min-height: 100vh;

    .login-content,
    .register-content {
      background: var(--bg-white);
    }
  }

  // Text content improvements
  .text-center {
    color: var(--text-color);

    a {
      color: var(--primary-color);

      &:hover {
        color: var(--primary-color);
        opacity: 0.8;
      }
    }
  }

  // Enhanced body styling for dark mode
  body {
    background: var(--bg-white);
    color: var(--text-color);
    transition: background-color 0.3s ease, color 0.3s ease;
  }

  // Enhanced scrollbar styling for dark mode
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: var(--bg-secondary);
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb {
    background: var(--border-color-base);
    border-radius: 4px;
    transition: background 0.2s ease;

    &:hover {
      background: var(--text-color-tertiary);
    }
  }

  ::-webkit-scrollbar-corner {
    background: var(--bg-secondary);
  }

  // Cart-specific dark mode improvements
  .table-cart {
    .ant-table {
      background: var(--component-background);
      border: 1px solid var(--border-color-base);
      border-radius: 12px;
      overflow: hidden;
      box-shadow: var(--shadow-2);

      .ant-table-thead > tr > th {
        background: var(--bg-secondary);
        border-bottom: 1px solid var(--border-color-base);
        color: var(--text-color);
        font-weight: 600;
        padding: 16px 12px;
      }

      .ant-table-tbody > tr > td {
        background: var(--component-background);
        border-bottom: 1px solid var(--border-color-split);
        color: var(--text-color);
        padding: 16px 12px;
        transition: all 0.2s ease;
      }

      .ant-table-tbody > tr:hover > td {
        background: var(--item-hover-bg);
      }

      .ant-table-tbody > tr:last-child > td {
        border-bottom: none;
      }
    }
  }

  // Quantity input styling
  .ant-input-number {
    background: var(--input-background);
    border-color: var(--border-color-base);
    border-radius: 6px;

    .ant-input-number-input {
      background: transparent;
      color: var(--text-color);
    }

    &:hover {
      border-color: var(--primary-color);
    }

    &:focus-within {
      border-color: var(--primary-color);
      box-shadow: 0 0 0 2px var(--primary-color-light);
    }
  }

  // Remove button styling
  .ant-btn.danger {
    background: var(--error-color);
    border-color: var(--error-color);
    color: var(--color-white);
    border-radius: 6px;
    transition: all 0.3s ease;

    &:hover {
      background: #ff4d4f;
      border-color: #ff4d4f;
      transform: translateY(-1px);
      box-shadow: var(--shadow-2);
    }

    &:focus {
      background: var(--error-color);
      border-color: var(--error-color);
      box-shadow: 0 0 0 2px rgba(240, 65, 52, 0.2);
    }
  }
}

// Cart form enhancements
.cart-form {
  background: var(--card-background);
  border: 1px solid var(--border-color-base);
  border-radius: 16px;
  box-shadow: var(--shadow-2);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: var(--shadow-3);
  }

  // Coupon form styling
  .coupon-form {
    display: flex;
    gap: 8px;
    align-items: stretch;

    .ant-input {
      flex: 1;
      background: var(--input-background);
      border-color: var(--border-color-base);
      border-radius: 8px;

      &:hover {
        border-color: var(--primary-color);
      }

      &:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 2px var(--primary-color-light);
      }
    }

    .ant-btn {
      border-radius: 8px;
      font-weight: 600;
      min-width: 120px;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-1px);
      }
    }
  }

  // Price display styling
  .initial-price {
    padding: 16px;
    background: var(--bg-secondary);
    border-radius: 12px;
    border: 1px solid var(--border-color-base);
    margin: 16px 0;

    strong {
      color: var(--text-color);
      font-weight: 700;
    }

    span {
      color: var(--primary-color);
      font-weight: 600;

      &.discount-price {
        color: var(--text-color-tertiary);
        text-decoration: line-through;
        opacity: 0.7;
      }
    }
  }

  // Payment gateway styling
  .payment-radio__wrapper {
    .payment-gateway,
    .payment-wallet {
      background: var(--bg-secondary);
      border-radius: 12px;
      padding: 16px;
      border: 1px solid var(--border-color-base);
      margin: 8px 0;
      transition: all 0.3s ease;

      &:hover {
        background: var(--item-hover-bg);
        border-color: var(--primary-color);
      }

      h4 {
        color: var(--text-color);
        font-weight: 600;
        margin-bottom: 12px;
        text-align: center;
      }

      .ant-radio-wrapper {
        color: var(--text-color);
        margin: 8px 0;
        padding: 8px 12px;
        border-radius: 8px;
        transition: all 0.3s ease;

        &:hover {
          background: var(--item-hover-bg);
        }

        .ant-radio {
          .ant-radio-inner {
            background: var(--component-background);
            border-color: var(--border-color-base);
          }

          &.ant-radio-checked .ant-radio-inner {
            background: var(--primary-color);
            border-color: var(--primary-color);
          }
        }

        img {
          filter: var(--image-filter, none);
          transition: filter 0.3s ease;
          border-radius: 4px;
        }
      }
    }
  }

  // Form inputs styling
  .ant-form-item {
    .ant-form-item-label > label {
      color: var(--text-color);
      font-weight: 500;

      .anticon {
        color: var(--primary-color);
        margin-right: 4px;
      }
    }

    .ant-input {
      background: var(--input-background);
      border-color: var(--border-color-base);
      color: var(--text-color);
      border-radius: 8px;
      transition: all 0.3s ease;

      &::placeholder {
        color: var(--text-color-tertiary);
      }

      &:hover {
        border-color: var(--primary-color);
      }

      &:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 2px var(--primary-color-light);
      }
    }

    .ant-form-item-explain-error {
      color: var(--error-color);
      font-size: 12px;
      margin-top: 4px;
    }
  }

  // Checkout button styling
  .ant-btn.primary {
    background: var(--gradient-primary);
    border: none;
    border-radius: 12px;
    height: 48px;
    font-weight: 700;
    font-size: 16px;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-1);

    &:hover {
      background: var(--primary-color);
      transform: translateY(-2px);
      box-shadow: var(--shadow-3);
    }

    &:focus {
      background: var(--primary-color);
      box-shadow: 0 0 0 3px var(--primary-color-light);
    }

    &:disabled {
      background: var(--bg-disabled);
      color: var(--text-color-disabled);
      transform: none;
      box-shadow: none;
    }
  }
}

// Global Ant Design tooltip and popover fixes for all themes
.ant-tooltip {
  .ant-tooltip-inner {
    background: var(--bg-modal) !important;
    color: var(--text-color) !important;
    border-radius: 6px !important;
    box-shadow: var(--shadow-2) !important;
  }

  .ant-tooltip-arrow::before,
  .ant-tooltip-arrow::after {
    background: var(--bg-modal) !important;
    border-color: var(--bg-modal) !important;
  }
}

.ant-popover {
  .ant-popover-inner {
    background: var(--bg-modal) !important;
    border: 1px solid var(--border-color-base) !important;
    border-radius: 8px !important;
    box-shadow: var(--shadow-3) !important;
  }

  .ant-popover-title {
    background: var(--bg-modal) !important;
    border-bottom: 1px solid var(--border-color-base) !important;
    color: var(--text-color) !important;
  }

  .ant-popover-inner-content {
    background: var(--bg-modal) !important;
    color: var(--text-color) !important;
  }

  .ant-popover-arrow::before,
  .ant-popover-arrow::after {
    background: var(--bg-modal) !important;
    border-color: var(--border-color-base) !important;
  }
}

.ant-dropdown-menu {
  background: var(--bg-modal) !important;
  border: 1px solid var(--border-color-base) !important;
  border-radius: 8px !important;
  box-shadow: var(--shadow-3) !important;

  .ant-dropdown-menu-item {
    color: var(--text-color) !important;
    transition: all 0.3s ease !important;

    &:hover {
      background: var(--item-hover-bg) !important;
      color: var(--primary-color) !important;
    }

    &:first-child {
      border-top-left-radius: 8px !important;
      border-top-right-radius: 8px !important;
    }

    &:last-child {
      border-bottom-left-radius: 8px !important;
      border-bottom-right-radius: 8px !important;
    }
  }

  .ant-dropdown-menu-item-divider {
    background-color: var(--border-color-base) !important;
  }
}

// Fix search input autofill in all themes
.ant-input:-webkit-autofill,
.ant-input:-webkit-autofill:hover,
.ant-input:-webkit-autofill:focus,
.ant-input:-webkit-autofill:active {
  -webkit-box-shadow: 0 0 0 30px var(--component-background) inset !important;
  -webkit-text-fill-color: var(--text-color) !important;
  background-color: var(--component-background) !important;
  background-image: none !important;
  transition: background-color 5000s ease-in-out 0s !important;
}

// Global clear icon styling for all input components
.ant-input-clear-icon {
  color: var(--text-color-secondary) !important;
  background: transparent !important;
  border-radius: 4px !important;
  transition: all 0.3s ease !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 16px !important;
  height: 16px !important;

  &:hover {
    color: var(--primary-color) !important;
    background: var(--item-hover-bg) !important;
  }

  .anticon {
    color: inherit !important;
    font-size: 12px !important;
  }
}

// Global search icon styling for all search components
.ant-input-search-button {
  .anticon-search {
    color: var(--text-color-secondary) !important;
    transition: color 0.3s ease !important;
  }
}

// Light mode specific styling for search button - better contrast
.light-mode,
.light-theme,
:root:not(.dark-mode):not(.dark-theme) {
  .ant-input-group-addon .ant-input-search-button {
    color: white !important;

    .anticon {
      color: inherit !important;
    }

    .anticon-search {
      color: inherit !important;
    }
  }
}

// Specific search icon styling for dark mode
.anticon-search {
  color: var(--text-color-secondary) !important;
  transition: color 0.3s ease !important;

  &:hover {
    color: var(--primary-color) !important;
  }
}

// High specificity search icon fixes
.ant-input-affix-wrapper .ant-input-suffix .anticon-search,
.ant-input-group-addon .ant-btn .anticon-search,
.ant-input-search .ant-input-group-addon .anticon-search,
.search-bar .ant-input-group-addon .anticon-search {
  color: var(--text-color-secondary) !important;
}

// Skeleton loading animation keyframes
@keyframes skeleton-loading {
  0% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0 50%;
  }
}

// Professional verification form enhancements for dark mode
.dark-mode {
  .creator-photo-verification,
  .model-photo-verification {
    .ant-form-item-label {
      label {
        color: var(--text-color) !important;
        font-weight: 600 !important;
        font-size: 14px !important;
        margin-bottom: 8px !important;
        display: block !important;
      }
    }

    .ant-form-item-explain {
      color: var(--text-color-secondary) !important;
      font-size: 12px !important;
      line-height: 1.4 !important;
      margin-top: 8px !important;
      padding: 8px 12px !important;
      background: var(--bg-secondary) !important;
      border-radius: 6px !important;
      border-left: 3px solid var(--primary-color) !important;
    }

    .document-upload {
      min-height: 180px !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      position: relative !important;

      // Mobile responsive adjustments
      @media (max-width: 768px) {
        min-height: 160px !important;
        padding: 15px !important;
      }

      @media (max-width: 480px) {
        min-height: 140px !important;
        padding: 12px !important;
      }

      // Enhanced image styling
      img,
      .ant-image {
        max-width: 100% !important;
        height: auto !important;
        object-fit: contain !important;
        border-radius: 8px !important;
        box-shadow: var(--shadow-1) !important;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;

        &:hover {
          transform: scale(1.02) !important;
          box-shadow: var(--shadow-2) !important;
        }
      }

      // Placeholder image styling
      img[src*='front-id.png'],
      img[src*='holding-id.jpg'] {
        opacity: 0.7 !important;
        filter: var(--image-filter, none) !important;
        border: 2px dashed var(--border-color-base) !important;
        background: var(--bg-secondary) !important;
        padding: 10px !important;

        &:hover {
          opacity: 0.9 !important;
          border-color: var(--primary-color) !important;
        }
      }
    }
  }

  // Ant Design Upload Drag component styling - Professional, modern, smart and intuitive UI
  .ant-upload.ant-upload-drag {
    background: var(--component-background) !important;
    border: 1px dashed var(--border-color-base) !important;
    color: var(--text-color) !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;

    &:hover:not(.ant-upload-disabled) {
      background: var(--bg-hover) !important;
      border-color: var(--primary-color) !important;
    }

    .ant-upload-drag-icon .anticon {
      color: var(--text-color-secondary) !important;
      transition: color 0.3s ease !important;

      &:hover {
        color: var(--primary-color) !important;
      }
    }

    .ant-upload-text {
      color: var(--text-color) !important;
    }

    .ant-upload-hint {
      color: var(--text-color-secondary) !important;
    }

    // Plus icon styling for upload areas
    .anticon-plus {
      color: var(--text-color-secondary) !important;
      transition: color 0.3s ease !important;

      &:hover {
        color: var(--primary-color) !important;
      }
    }

    // Disabled state
    &.ant-upload-disabled {
      background: var(--bg-disabled) !important;
      border-color: var(--border-color-base) !important;
      color: var(--text-color-disabled) !important;
      cursor: not-allowed !important;

      .ant-upload-drag-icon .anticon,
      .ant-upload-text,
      .ant-upload-hint,
      .anticon-plus {
        color: var(--text-color-disabled) !important;
      }
    }
  }

  // Enhanced text center styling
  .text-center {
    text-align: center !important;
    color: var(--text-color) !important;
  }

  // Row and column enhancements for verification forms
  .ant-row {
    .ant-col {
      // Mobile spacing improvements
      @media (max-width: 768px) {
        margin-bottom: 20px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }

  // Input group addon dark mode fixes - Professional, modern, smart and intuitive UI
  .ant-input-group-addon {
    border-color: var(--border-color-base) !important;
    color: var(--text-color) !important;
    transition: all 0.3s ease !important;

    &:hover {
      color: var(--text-color) !important;
    }

    &:focus {
      background-color: var(--bg-secondary) !important;
      border-color: var(--primary-color) !important;
      box-shadow: 0 0 0 2px var(--primary-color-light) !important;
    }

    // Dark mode specific styling for search button
    .ant-input-search-button {
      color: var(--text-color-secondary) !important;

      &:hover {
        color: var(--primary-color) !important;
      }

      .anticon {
        color: inherit !important;
      }

      .anticon-search {
        color: inherit !important;
      }
    }
  }

  // Input number affix wrapper disabled state dark mode fix
  .ant-input-number-affix-wrapper-disabled {
    color: var(--text-color-disabled) !important;
    background-color: var(--bg-disabled) !important;
    border-color: var(--border-color-split) !important;
    box-shadow: none !important;
    cursor: not-allowed !important;
    opacity: 0.6 !important;

    .ant-input-number-input {
      color: var(--text-color-disabled) !important;
      background: transparent !important;
      cursor: not-allowed !important;
    }

    .ant-input-number-handler-wrap {
      background: var(--bg-disabled) !important;
      border-left-color: var(--border-color-split) !important;

      .ant-input-number-handler {
        color: var(--text-color-disabled) !important;
        background: transparent !important;
        cursor: not-allowed !important;
        border-color: var(--border-color-split) !important;

        &:hover {
          background: transparent !important;
          color: var(--text-color-disabled) !important;
          cursor: not-allowed !important;
        }
      }
    }

    &:hover {
      border-color: var(--border-color-split) !important;
      box-shadow: none !important;
    }
  }

  // Collapse component dark mode fixes - Professional, modern, smart and intuitive UI
  .ant-collapse {
    background: var(--component-background) !important;
    border-color: var(--border-color-base) !important;

    > .ant-collapse-item {
      border-color: var(--border-color-base) !important;

      > .ant-collapse-header {
        color: var(--text-color) !important;
        background: var(--component-background) !important;
        border-color: var(--border-color-base) !important;
        transition: all 0.3s ease !important;

        &:hover {
          background: var(--item-hover-bg) !important;
          color: var(--text-color) !important;
        }

        &:focus {
          background: var(--item-hover-bg) !important;
          color: var(--text-color) !important;
        }

        .ant-collapse-arrow {
          color: var(--text-color-secondary) !important;
          transition: color 0.3s ease !important;

          &:hover {
            color: var(--primary-color) !important;
          }
        }

        .ant-collapse-expand-icon {
          color: var(--text-color-secondary) !important;

          &:hover {
            color: var(--primary-color) !important;
          }
        }
      }

      &.ant-collapse-item-active {
        > .ant-collapse-header {
          color: var(--text-color) !important;
          background: var(--item-active-bg) !important;

          .ant-collapse-arrow {
            color: var(--primary-color) !important;
          }
        }
      }

      &.ant-collapse-item-disabled {
        > .ant-collapse-header {
          color: var(--text-color-disabled) !important;
          background: var(--bg-disabled) !important;
          cursor: not-allowed !important;

          .ant-collapse-arrow {
            color: var(--text-color-disabled) !important;
          }
        }
      }

      .ant-collapse-content {
        background: var(--component-background) !important;
        border-color: var(--border-color-base) !important;

        .ant-collapse-content-box {
          color: var(--text-color) !important;
        }
      }
    }

    // Borderless collapse variant
    &.ant-collapse-borderless {
      background: transparent !important;

      > .ant-collapse-item {
        border: none !important;

        > .ant-collapse-header {
          background: transparent !important;
        }

        .ant-collapse-content {
          background: transparent !important;
          border: none !important;
        }
      }
    }

    // Ghost collapse variant
    &.ant-collapse-ghost {
      background: transparent !important;

      > .ant-collapse-item {
        border: none !important;

        > .ant-collapse-header {
          background: transparent !important;
          padding-left: 0 !important;
          padding-right: 0 !important;
        }

        .ant-collapse-content {
          background: transparent !important;
          border: none !important;

          .ant-collapse-content-box {
            padding-left: 0 !important;
            padding-right: 0 !important;
          }
        }
      }
    }
  }

  // Ant Design tabs component dark mode fixes - Professional, modern, smart and intuitive UI
  .ant-tabs {
    color: var(--text-color) !important;
    transition: color 0.3s ease !important;

    // Tab navigation styling
    .ant-tabs-nav {
      border-color: var(--border-color-base) !important;

      &::before {
        border-color: var(--border-color-base) !important;
      }
    }

    // Individual tab styling
    .ant-tabs-tab {
      color: var(--text-color) !important;
      transition: color 0.3s ease, background-color 0.3s ease !important;

      &:hover {
        color: var(--primary-color) !important;
      }

      // Tab button content
      .ant-tabs-tab-btn {
        color: var(--text-color) !important;
        transition: color 0.3s ease !important;

        &:hover {
          color: var(--primary-color) !important;
        }
      }
    }

    // Active tab styling
    .ant-tabs-tab-active {
      color: var(--primary-color) !important;

      .ant-tabs-tab-btn {
        color: var(--primary-color) !important;
      }
    }

    // Tab ink bar (active indicator)
    .ant-tabs-ink-bar {
      background: var(--primary-color) !important;
    }

    // Tab content area
    .ant-tabs-content-holder {
      background: var(--component-background) !important;
    }

    // Tab pane content
    .ant-tabs-tabpane {
      color: var(--text-color) !important;
    }
  }
}

// Global input number affix wrapper disabled state override
// This ensures proper styling in both light and dark modes
.ant-input-number-affix-wrapper-disabled {
  color: var(--text-color-disabled, rgba(0, 0, 0, 0.25)) !important;
  background-color: var(--bg-disabled, #f5f5f5) !important;
  border-color: var(--border-color-split, #e5e5e5) !important;
  box-shadow: none !important;
  cursor: not-allowed !important;
  opacity: 1 !important;

  .ant-input-number-input {
    color: var(--text-color-disabled, rgba(0, 0, 0, 0.25)) !important;
    background: transparent !important;
    cursor: not-allowed !important;
  }

  .ant-input-number-handler-wrap {
    background: var(--bg-disabled, #f5f5f5) !important;
    border-left-color: var(--border-color-split, #e5e5e5) !important;

    .ant-input-number-handler {
      color: var(--text-color-disabled, rgba(0, 0, 0, 0.25)) !important;
      background: transparent !important;
      cursor: not-allowed !important;
      border-color: var(--border-color-split, #e5e5e5) !important;

      &:hover {
        background: transparent !important;
        color: var(--text-color-disabled, rgba(0, 0, 0, 0.25)) !important;
        cursor: not-allowed !important;
      }
    }
  }

  &:hover {
    border-color: var(--border-color-split, #e5e5e5) !important;
    box-shadow: none !important;
  }
}
