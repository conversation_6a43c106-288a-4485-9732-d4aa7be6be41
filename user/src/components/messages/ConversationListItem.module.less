@import '../../../style/vars.less';

.conversation-list-item {
  display: flex;
  align-items: center;
  padding: 10px;
  position: relative;
}

.conversation-left-corner {
  display: flex;
  flex-direction: column;
  margin-right: 10px;
  align-items: center;
  justify-content: center;
  position: relative;
}

.conversation-left-corner span {
  font-size: 12px;
}

.conversation-list-item {
  :global .online {
    position: absolute;
    left: -1px;
    top: 4px;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: var(--success-color);

    .active {
      display: block;
      background-color: var(--text-color-tertiary);
      width: 10px;
      height: 10px;
      border-radius: 50%;
      animation: blink 1s linear infinite;
      opacity: 0.7;
    }
  }

  :global .offline {
    position: absolute;
    left: -1px;
    top: 4px;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: var(--text-color-disabled);
  }

  :global .conversation-snippet {
    margin: 0;
  }
}

.conversation-list-item:hover {
  background: var(--item-hover-bg, rgba(0, 0, 0, 0.04));
  cursor: pointer;
  transition: all 0.3s ease;
}

.conversation-list-item.active {
  background: var(--item-active-bg, rgba(0, 0, 0, 0.06));
  border-left: 3px solid var(--primary-color);
}

.conversation-photo {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.conversation-title {
  font-size: 14px;
  font-weight: 600;
  text-transform: capitalize;
  margin: 0;
  color: var(--text-color);
}

.conversation-snippet {
  font-size: 12px;
  color: var(--text-color-secondary);
  margin: 0;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.conversation-time {
  font-size: 12px;
  color: var(--text-color-tertiary);
  margin: 0;
}

.notification-badge {
  position: absolute;
  top: 40px;
  left: 10px;
}

.conversation-top-right {
  position: absolute;
  right: 0;
  font-size: 16px;
  z-index: 1;
  cursor: pointer;
  margin: 0 5px;
  border: unset;
  top: 50%;
  transform: translate(0%, -50%);
  background-color: unset;
  box-shadow: none;

  &:hover,
  &:active,
  &:focus {
    background-color: unset;
    color: var(--text-color-secondary);
  }
}

.conversation-top-right-unpin {
  position: absolute;
  right: 0;
  font-size: 20px;
  z-index: 1;
  cursor: pointer;
  margin: 0 5px;
  border: unset;
  top: 50%;
  transform: translate(0%, -50%);
  background-color: unset;
  &:hover,
  &:active,
  &:focus {
    background-color: unset;
    color: var(--text-color-secondary);
  }
}

.group-conversation {
  position: relative;
}
