@import '../../../style/vars.less';

.feed-card {
  border-bottom: 1px solid var(--border-color-split);
  background: var(--card-background);
  border-radius: 12px;
  margin-bottom: 16px;
  box-shadow: var(--shadow-1);
  transition: all 0.3s ease;
  overflow: hidden;

  &:hover {
    box-shadow: var(--shadow-2);
    transform: translateY(-2px);
  }

  &:last-child {
    border: 0;
  }

  :global .feed-top {
    display: flex;
    justify-content: space-between;
    padding: 16px 20px;
    padding-bottom: 12px;
    background: var(--card-background);

    .feed-top-left {
      cursor: pointer;
      display: flex;
      position: relative;
      transition: transform 0.3s ease;

      &:hover {
        transform: scale(1.02);
      }

      img {
        width: 44px;
        height: 44px;
        border-radius: 50%;
        border: 2px solid var(--border-color-light);
        transition: border-color 0.3s ease;

        &:hover {
          border-color: var(--primary-color);
        }
      }

      .feed-name {
        padding: 0 12px;

        h4 {
          font-size: 15px;
          margin: 0;
          text-transform: capitalize;
          color: var(--text-color);
          font-weight: 600;
        }

        h5 {
          font-size: 13px;
          color: var(--text-color-secondary);
          margin: 2px 0 0 0;
        }
      }

      .online-status {
        width: 10px;
        height: 10px;
        background-color: var(--success-color);
        border-radius: 50%;
        position: absolute;
        bottom: 2px;
        left: 34px;
        border: 2px solid var(--card-background);
        box-shadow: var(--shadow-1);

        &.off {
          background-color: var(--text-color-disabled);
        }
      }
    }

    .feed-top-right {
      .feed-time {
        font-size: 13px;
        color: var(--text-color-tertiary);
        margin: 0 10px;
        white-space: nowrap;
        text-align: right;
        font-weight: 500;
      }
    }
  }
  :global .feed-text {
    font-size: 14px;
    padding: 0 20px 16px;
    color: var(--text-color);
    line-height: 1.6;
  }

  :global .feed-comment {
    padding: 16px 20px 0;
    border-top: 1px solid var(--border-color-split);
    background: var(--card-background);
  }

  :global .feed-container {
    .feed-content {
      position: relative;
      .videojs-player .video-js {
        height: auto;
        background-color: var(--component-background);

        .vjs-tech {
          position: relative;
        }

        .vjs-poster {
          background-color: var(--component-background) !important;
        }

        @media screen and (max-width: @mobile-screen) {
          .vjs-tech {
            border-radius: 10px;
          }
        }
      }
    }

    .lock-content {
      position: relative;
      background-image: url(/leaf.jpg);
      background-size: cover;
      background-repeat: no-repeat;
      background-position: center;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20px;
      transition: all 0.5s ease-in-out;
      padding: 20% 0;
      .anticon-lock,
      .anticon-unlock {
        font-size: 35px;
        color: var(--color-white);
        margin-bottom: 10px;
      }

      .count-media {
        position: absolute;
        bottom: 10px;
        color: var(--color-white);
        text-align: center;
        background: rgba(0, 0, 0, 0.75);
        border-radius: 5px;
        padding: 5px 15px;
        font-size: 15px;
      }

      p {
        text-transform: uppercase;
        font-size: 14px;
        font-weight: 600;
        color: var(--color-white);
        padding: 5px 15px;
        border: 1px solid;
        border-radius: 25px;
        font-weight: 600;
        background-color: rgba(0, 0, 0, 0.75);

        &:hover {
          opacity: 0.8;
        }
      }
    }
  }

  :global .feed-actions {
    display: flex;
    width: 100%;
    transition: all 0.3s ease-in-out;
    .action-item {
      width: 100%;
      padding: 5px 10px;

      button {
        cursor: pointer;
        padding: 5px;
        color: #888;
        display: inline-block;
        vertical-align: middle;
        border: 0;
        box-shadow: none;
        margin: 0 10px;
        [class~='anticon'] {
          font-size: 20px;
        }

        &:hover,
        &.active {
          color: @theme-color;
        }

        &.liked {
          color: @primary-color;
        }
      }

      // &:last-child {
      //   text-align: right;
      //   justify-content: flex-end;
      //   width: 25%;
      // }

      .sc-bdVaJa {
        padding: 5px;
        margin-top: -3px;

        .ewcqji {
          margin: 0 5px;
        }

        .sc-htpNat.cgeQjm {
          svg {
            margin: 0;
          }

          span {
            display: none;
          }
        }
      }
    }
  }

  :global .videojs-player {
    width: 100%;

    .video-js {
      width: 100%;
      margin-bottom: 10px;

      .vjs-big-play-button {
        top: calc(50% - 24px);
        left: calc(50% - 45px);
      }
    }
  }

  :global .feed-image {
    object-fit: fill;
  }
}
.button-feed {
  button {
    margin-right: 10px;
    margin-bottom: 5px;
    @media screen and (max-width: @mobile-screen) {
      margin-right: 5px;
      padding: 5px;
    }
  }
}
.f-upload-list {
  display: flex;
  width: 100%;
  overflow: auto;
  flex-wrap: wrap;
  margin: 0 -5px;
  :global .f-upload-item {
    border-radius: 5px;
    overflow: hidden;
    padding: 5px;
    background-color: var(--bg-secondary, @light-blue);
    position: relative;
    margin: 5px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    border: 1px solid var(--border-color-split, transparent);
    transition: background-color 0.3s ease, border-color 0.3s ease;

    .f-upload-thumb {
      width: 120px;
      height: 120px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: var(--component-background);
      border: 1px solid var(--border-color-base);
      border-radius: 5px;
      overflow: hidden;
      position: relative;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .f-thumb-vid {
        width: 100%;
        height: 100%;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .play-icon {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          color: var(--color-white);
          font-size: 24px;
          padding: 5px;
          border-radius: 50%;
        }
      }
    }

    .f-remove {
      padding: 8px;
      border-radius: 50%;
      cursor: pointer;
      color: var(--color-white);
      font-size: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-left: auto; /* Đẩy icon delete về bên phải */
      transition: color 0.3s ease;
    }
  }

  :global .add-more {
    display: flex;
    height: 130px;
    align-self: center;
    justify-content: center;
    width: 65px;
    background-color: var(--bg-disabled, @light-grey);
    border-radius: 5px;
    margin-left: 5px;
    border: 1px solid var(--border-color-split, transparent);
    transition: background-color 0.3s ease, border-color 0.3s ease;

    &:hover {
      opacity: 0.8;
    }

    cursor: pointer;

    .ant-upload {
      height: 130px;
      width: 65px;
      display: flex;
      align-items: center;
      justify-content: center;

      .anticon.anticon-plus {
        font-size: 20px;
      }
    }
  }
}

.feed-slider {
  position: relative;
  overflow-x: hidden;

  :global .ant-carousel {
    border-radius: 5px;

    .slick-slide {
      .img-slide {
        position: relative;
        background-color: var(--color-black);
        .blur-img-slider {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-repeat: no-repeat;
          background-size: cover;
          background-position: center center;
          transform: scale(1.1);
          filter: blur(20px);
          opacity: 0.7;
        }
      }
      .ant-image {
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        a,
        img {
          max-width: 100%;
          height: 60dvh;
          object-fit: fill;
        }
        .ant-image-mask {
          opacity: 0;
        }
      }
    }
  }

  :global .count-media {
    position: absolute;
    bottom: 35px;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .count-media-item {
      background: rgba(121, 121, 121, 0.4);
      color: var(--color-white);
      padding: 5px;
      border-radius: 5px;
      font-size: 12px;
      z-index: 999;
    }
  }

  :global .proccessing {
    background-image: url('/leaf.jpg');
    background-size: cover;
    background-repeat: no-repeat;
    display: flex;
    width: 100%;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    padding: 10% 0;
  }
}

.feed-input {
  padding-right: 30px;

  &:focus {
    background-color: var(--bg-hover, @placeholder-input);
  }
}

.ant-input-number-input-wrap input {
  height: 30px;
}

.input-f-desc {
  position: relative;
  margin-bottom: 10px;

  :global .grp-emotions {
    z-index: 9;
    position: absolute;
    bottom: 5px;
    right: 5px;

    .emoji-picker-react {
      bottom: unset;
      top: 505;
    }
  }

  :global .grp-colors {
    z-index: 9;
    position: absolute;
    bottom: 5px;
    right: 35px;

    .anticon {
      font-size: 22px;
      color: var(--primary-color);
    }

    .colors {
      position: absolute;
      top: 50%;
      right: 0;
      display: none;
    }

    &:hover {
      .colors {
        display: block;
      }
    }
  }
}

.poll-form {
  width: 100%;
  max-width: 320px;
  padding: 5px;
  border-radius: 5px;
  background-color: var(--bg-secondary, @light-blue);
  margin-bottom: 10px;
  border: 1px solid var(--border-color-split, transparent);
  transition: background-color 0.3s ease, border-color 0.3s ease;

  :global .poll-top {
    padding: 5px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;

    span {
      cursor: pointer;

      &:hover {
        color: var(--primary-color);
      }
    }

    a {
      color: var(--color-white);
      padding: 1px 7px;
      border-radius: 50%;
      background-color: var(--primary-color);
      transition: background-color 0.3s ease;
    }
  }

  :global .poll-input {
    margin-bottom: 10px;
  }

  :global .poll-expiration {
    width: 100%;
    padding: 0 15px;
    display: flex;
    justify-content: space-between;
  }
}

.feed-polls {
  padding: 0 10px;
  .feed-poll {
    display: flex;
    background-color: var(--bg-disabled, @light-grey);
    padding: 5px 10px;
    border-radius: 3px;
    justify-content: space-between;
    cursor: pointer;
    margin: 5px 0 0 0;
    border: 1px solid var(--border-color-split, transparent);
    transition: background-color 0.3s ease, border-color 0.3s ease;
    &:first-child {
      margin: 10px 0 0 0;
    }
    &:hover {
      opacity: 0.8;
    }
  }
}

.total-vote {
  display: flex;
  width: 100%;
  justify-content: space-between;
  margin-top: 10px;
  margin-bottom: 15px;
  color: var(--text-color-secondary);
}

.story-switch-type {
  display: flex;
  flex-wrap: wrap;
  max-width: 660px;
  margin: auto;
  padding: 10px;

  :global .type-item {
    width: calc(50% - 20px);
    margin: 10px;
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
    flex-wrap: wrap;
    padding: 100px 5px;
    border-radius: 8px;
    color: var(--color-white);
    box-shadow: 1px 1px 5px 0px rgba(0, 0, 0, 0.75);
    cursor: pointer;
    opacity: 0.8;

    &:hover {
      opacity: 1;
    }

    &.left {
      background: linear-gradient(
        90deg,
        @theme-color 10%,
        @primary-color 50%,
        @theme-color 90%
      );
    }

    &.right {
      background: linear-gradient(
        180deg,
        @orange-color 10%,
        @secondary-color 50%,
        @orange-color 90%
      );
    }

    &.middle {
      margin: auto;
      background: linear-gradient(
        180deg,
        @info-color 10%,
        @processing-color 50%,
        @info-color 90%
      );
    }

    span {
      padding: 5px 10px;
      font-size: 40px;
      color: var(--color-white);

      .anticon {
        font-size: 40px;
        color: var(--color-white);
      }
    }

    @media screen and (max-width: @mobile-screen) {
      padding: 50px 5px;
    }
  }
}

.submit-btns {
  justify-content: space-around;
  margin: 10px auto;
  width: 200px;
  display: flex;
}

.search-feed-bar {
  display: flex;
  width: 100%;
  margin: 0 0 5px;
  padding: 0 10px;
  justify-content: space-between;
  align-items: center;

  :global .ant-picker.ant-picker-range {
    width: 100%;
    padding-right: 30px;

    input {
      height: 30px;
      padding: 0 5px;
    }
  }

  :global .ant-input-group-wrapper.ant-input-search {
    width: 100%;

    input {
      height: 20px;
    }
  }

  :global .grid-btns {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    align-items: center;

    a {
      margin: 0 5px;
      padding: 0 5px;
      font-size: 20px;
      color: var(--text-color-secondary);
      border-radius: 3px;
      transition: color 0.3s ease;

      &.active {
        color: var(--primary-color);
      }
    }
  }

  @media screen and (max-width: @mobile-screen) {
    :global .ant-picker.ant-picker-range {
      padding-right: 5px;
    }

    :global .ant-input-group-wrapper.ant-input-search {
      padding-right: 5px;
    }

    :global .grid-btns {
      a {
        margin: 0;
      }
    }
  }
}

.grid-view {
  display: flex;
  width: 100%;
  flex-wrap: wrap;

  :global .grid-card {
    width: 33.33%;
    padding: 0 5px;
    margin-bottom: 10px;

    .card-thumb {
      position: relative;
      height: 400px;
      background-color: var(--bg-disabled, @light-grey);
      background-image: url(/leaf.jpg);
      background-size: cover;
      background-repeat: no-repeat;
      background-position: center;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20px;
      border-radius: 5px;
      cursor: pointer;
      border: 1px solid var(--border-color-split, transparent);
      transition: background-color 0.3s ease, border-color 0.3s ease;

      &:hover {
        opacity: 0.9;
      }

      .anticon-lock,
      .anticon-unlock {
        font-size: 35px;
        color: var(--primary-color);
      }

      .card-bottom {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 5px;

        .count-media-item {
          background: rgba(121, 121, 121, 0.4);
          color: var(--color-white);
          padding: 5px;
          border-radius: 5px;
          font-size: 12px;
          z-index: 999;
        }

        .stats {
          background: rgba(121, 121, 121, 0.4);
          padding: 5px;
          border-radius: 5px;
          font-size: 12px;
          z-index: 999;
          display: flex;

          a {
            margin-right: 10px;
            color: var(--color-white);
          }
        }
      }

      .feed-info {
        position: absolute;
        top: 5px;
        right: 5px;
        background-color: rgba(121, 121, 121, 0.4);
        border-radius: 50%;
        padding: 0 4px;
        color: var(--color-white);
      }
    }
  }

  @media screen and (max-width: @mobile-screen) {
    :global .grid-card {
      width: 50%;

      .card-thumb {
        height: 250px;
      }
    }
  }
}

.purchase-feed-form {
  text-align: center;
  padding: 20px;
  @media screen and (max-width: @mobile-screen) {
    padding: 0;
  }
  :global {
    h3 {
      font-weight: bold;
      font-size: 17px;
    }
    .ant-radio-group {
      margin: 15px 0;
      .ant-radio-wrapper {
        margin: 5px 0;
        span {
          display: inline-block;
          vertical-align: middle;
          margin: 0 0 0 5px;
          padding: 0;
          &:last-child {
            margin-top: -3px;
          }
          &.text {
            margin-top: 0;
          }
        }
      }
    }
  }
}

.tip-performer-modal {
  :global .ant-modal-content {
    .ant-modal-body {
      padding: 0;
    }
  }
}

.ppv-badge {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.75);
  padding: 6px 12px;
  border-radius: 20px;
  color: var(--color-white);
  display: flex;
  align-items: center;
  gap: 8px;
  backdrop-filter: blur(4px);
  z-index: 2;
  font-weight: 500;

  :global(.anticon) {
    color: var(--warning-color);
    font-size: 16px;
  }

  span {
    font-size: 14px;
  }
}

.schedule-section {
  margin: 24px 0;
  padding: 16px;
  background: var(--bg-secondary);
  border-radius: 8px;
  border: 1px solid var(--border-color-split, transparent);
  transition: background-color 0.3s ease, border-color 0.3s ease;

  .schedule-header {
    margin-bottom: 16px;

    span {
      color: var(--text-color);
      font-weight: 500;
      margin-right: 8px;
    }

    .anticon {
      color: var(--primary-color);
      font-size: 16px;
    }
  }

  .schedule-picker {
    width: 100%;

    :global {
      .ant-picker {
        width: 100%;

        @media (min-width: 768px) {
          width: 320px;
        }
      }
    }
  }
}

@media (max-width: 767px) {
  .schedule-section {
    padding: 12px;
    margin: 16px 0;
  }
}

// Dark mode specific enhancements for feed components
:global(.dark-mode) {
  .feed-container {
    .feed-content {
      .videojs-player .video-js {
        background-color: var(--component-background) !important;
        border: 1px solid var(--border-color-base);

        .vjs-poster {
          background-color: var(--component-background) !important;
        }

        .vjs-control-bar {
          background: rgba(0, 0, 0, 0.8) !important;
        }

        .vjs-big-play-button {
          background: rgba(0, 0, 0, 0.7) !important;
          border-color: var(--primary-color) !important;

          &:hover {
            background: var(--primary-color) !important;
          }
        }
      }
    }

    .lock-content {
      background-color: var(--bg-secondary);
      border: 1px solid var(--border-color-base);
    }
  }

  .feed-actions {
    .action-item {
      button {
        color: var(--text-color-secondary) !important;
        background: transparent !important;

        &:hover,
        &.active {
          color: var(--primary-color) !important;
        }

        &.liked {
          color: var(--primary-color) !important;
        }
      }
    }
  }

  .f-upload-list {
    .f-upload-item {
      background-color: var(--bg-card) !important;
      border-color: var(--border-color-base) !important;

      .f-upload-thumb {
        background: var(--component-background) !important;
        border-color: var(--border-color-base) !important;
      }

      .f-remove {
        color: var(--text-color-secondary) !important;

        &:hover {
          color: var(--error-color) !important;
        }
      }
    }

    .add-more {
      background-color: var(--bg-disabled) !important;
      border-color: var(--border-color-base) !important;

      &:hover {
        background-color: var(--item-hover-bg) !important;
      }

      .ant-upload {
        color: var(--text-color-secondary);
      }
    }
  }

  .feed-slider {
    .ant-carousel {
      .slick-slide {
        .img-slide {
          background-color: var(--bg-secondary) !important;
        }
      }
    }
  }

  .poll-form {
    background-color: var(--bg-card) !important;
    border-color: var(--border-color-base) !important;
  }

  .feed-polls {
    .feed-poll {
      background-color: var(--bg-card) !important;
      border-color: var(--border-color-base) !important;
      color: var(--text-color) !important;

      &:hover {
        background-color: var(--item-hover-bg) !important;
      }
    }
  }

  .story-switch-type {
    .type-item {
      background-color: var(--bg-card) !important;
      border: 1px solid var(--border-color-base) !important;
      box-shadow: var(--shadow-2) !important;

      &:hover {
        background-color: var(--item-hover-bg) !important;
      }
    }
  }
}
