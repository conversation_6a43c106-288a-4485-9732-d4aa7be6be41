@import '../../../style/vars.less';

.prd-card {
  background: var(--bg-card);
  box-shadow: var(--shadow-2);
  position: relative;
  overflow: hidden;
  border-radius: 12px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid var(--border-color-light);

  &:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-3);
  }

  :global .prd-button {
    width: 100%;
    display: flex;
    justify-content: space-around;

    .ant-btn.primary {
      width: 100%;
      border-radius: 0 0 12px 12px;
      border: none;
      opacity: 1;
      transition: none;
      font-size: 13px;
      padding: 8px;
      background: var(--gradient-primary);
      color: var(--color-white);

      &:hover {
        background: var(--primary-color);
      }
    }

    @media screen and (max-width: @mobile-screen) {
      .ant-btn.primary,
      .ant-btn.secondary {
        min-height: 32px;
        height: 32px;
        padding: 2px;
        font-size: 9px;

        .anticon + span,
        .ant-btn > span + .anticon {
          margin-left: 2px;
        }
      }
    }
  }

  :global .label-wrapper {
    position: absolute;
    right: auto;
    left: 0px;
    top: 5px;
    display: flex;
    flex-direction: column;
    z-index: 1;

    .label-wrapper-price,
    .label-wrapper-digital {
      color: var(--color-white);
      background: var(--text-color);
      clip-path: polygon(0px 0px, 0px 100%, 100% 100%, 90% 50%, 100% 0);
      font-size: 12px;
      font-weight: 600;
      padding: 3px 13px;
      margin-bottom: 5px;
      box-shadow: var(--shadow-1);

      @media screen and (max-width: @mobile-screen) {
        font-size: 12px;
        margin: 1px 0;
      }
    }

    .label-wrapper-digital {
      background: var(--gradient-primary);
    }
  }
}

.prd-thumb {
  padding-top: 75%;
  position: relative;
  img {
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    object-fit: cover;
  }
}

.no-of-images {
  position: absolute;
  top: 5px;
  right: 5px;
  color: var(--color-white);
  font-size: 13px;
  z-index: 0;

  @media screen and (max-width: @mobile-screen) {
    font-size: 12px;
  }
}

.prd-info {
  transition: all 0.3s ease-in-out;
  width: 100%;
  padding: 8px 12px;
  color: var(--color-white);
  position: absolute;
  bottom: 40px;
  background: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0) 0%,
    rgba(0, 0, 0, 0.8) 100%
  );
  backdrop-filter: blur(4px);
  z-index: 1;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  font-size: 15px;
  text-align: center;
  text-transform: capitalize;
  font-weight: 600;

  @media screen and (max-width: @mobile-screen) {
    bottom: 32px;
    font-size: 14px;
  }
}

// Dark mode specific overrides for better contrast and visibility
:global(.dark-mode) {
  .prd-card {
    background: var(--bg-card) !important;
    border-color: var(--border-color-base) !important;
    box-shadow: var(--shadow-2) !important;

    &:hover {
      box-shadow: var(--shadow-3) !important;
      border-color: var(--primary-color) !important;
    }

    :global .label-wrapper {
      .label-wrapper-price {
        background: var(--primary-color) !important;
        color: var(--color-white) !important;
        box-shadow: var(--shadow-2) !important;
      }

      .label-wrapper-digital {
        background: var(--gradient-primary) !important;
        color: var(--color-white) !important;
        box-shadow: var(--shadow-2) !important;
      }
    }

    .prd-info {
      background: linear-gradient(
        to bottom,
        rgba(0, 0, 0, 0) 0%,
        rgba(0, 0, 0, 0.9) 100%
      ) !important;
      color: var(--color-white) !important;
      backdrop-filter: blur(6px) !important;
    }

    .no-of-images {
      color: var(--color-white) !important;
      text-shadow: 0 1px 3px rgba(0, 0, 0, 0.8) !important;
    }

    :global .prd-button {
      .ant-btn.primary {
        background: var(--gradient-primary) !important;
        color: var(--color-white) !important;
        border: none !important;
        transition: none !important;

        &:hover {
          background: var(--primary-color-hover) !important;
          color: var(--color-white) !important;
        }

        &:disabled {
          background: var(--bg-disabled) !important;
          color: var(--text-color-disabled) !important;
        }
      }
    }
  }
}
