@import '../../../style/vars.less';

.notification-page {
  max-width: 800px;
  margin: 30px auto;
  padding: 0 20px;
  background: var(--component-background);
  border-radius: 12px;
  box-shadow: var(--shadow-1);
  min-height: 400px;
}

.notification-page-header {
  overflow: hidden;
  margin-bottom: 20px;
  padding: 20px 0;
  border-bottom: 1px solid var(--border-color-split);

  .btn-dismiss-all {
    float: right;
    padding: 8px 16px;
    border: 1px solid var(--border-color-base);
    color: var(--primary-color);
    background: transparent;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.2s ease;

    &:hover {
      border-color: var(--primary-color);
      background: var(--item-hover-bg);
      color: var(--primary-color);
      transform: translateY(-1px);
      box-shadow: 0 2px 4px rgba(139, 109, 209, 0.2);
    }

    &:focus {
      border-color: var(--primary-color);
      background: var(--item-hover-bg);
      color: var(--primary-color);
      outline: none;
      box-shadow: 0 0 0 2px rgba(139, 109, 209, 0.2);
    }
  }

  h3 {
    font-weight: 600;
    float: left;
    margin: 8px 0;
    color: var(--text-color);
    font-size: 20px;
  }
}
