@import '../node_modules/antd/dist/antd.less';

// Core design system and variables
@import './vars.less'; // Variables shared across all styles
@import './default.less'; // Default theme variables and base styles
@import './theme.less'; // Theme implementation (light/dark)

// Layout and base styles
@import './global.less'; // Global layout and component styles
@import './responsive.less'; // Responsive breakpoints and styles

// Specific features styles
@import './videojs.less'; // Video.js player styles

// Dark mode and overrides
@import './app.less'; // App-specific styles and dark mode

@import 'suneditor/dist/css/suneditor.min.css'; // Import Sun Editor's CSS File

html {
  scroll-behavior: smooth;
}

body {
  background-color: var(--bg-white);
  color: var(--text-color, @text-color);
  font-size: 14px;
  line-height: 23px;
  font-family: 'Roboto', sans-serif;
  transition: background-color 0.3s ease, color 0.3s ease;
}

li {
  list-style: none;
}

a {
  text-decoration: none;
}

.block-ip-page {
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;

  .ant-result {
    .ant-result-title {
      font-size: 74px;
    }

    .ant-result-subtitle {
      font-size: 24px;
    }
  }
}

.sun-editor-editable {
  background-color: transparent !important;
  color: var(--text-color) !important;

  // Universal selector for maximum coverage
  * {
    color: inherit !important;
    background-color: transparent !important;
  }

  // Basic text elements
  p,
  div,
  span,
  li,
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    color: var(--text-color) !important;
  }

  // Paragraphs with proper spacing
  p {
    color: var(--text-color-secondary) !important;
    line-height: 1.6;
    margin-bottom: 12px;
  }

  // Headings with proper hierarchy
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    color: var(--text-color) !important;
    font-weight: 600;
    margin-bottom: 16px;
  }

  // Lists
  ul,
  ol {
    color: var(--text-color-secondary) !important;
    margin-bottom: 12px;

    li {
      color: var(--text-color-secondary) !important;
      line-height: 1.5;
      margin-bottom: 8px;
    }
  }

  // Links with all states
  a {
    color: var(--primary-color) !important;
    text-decoration: none;
    transition: all 0.3s ease;

    &:hover {
      color: var(--primary-color) !important;
      opacity: 0.8;
      text-decoration: underline;
    }

    &:visited {
      color: var(--primary-color) !important;
    }

    &:active {
      color: var(--primary-color) !important;
    }
  }

  // Text formatting
  strong,
  b {
    color: var(--text-color) !important;
    font-weight: 600;
  }

  em,
  i {
    color: var(--text-color-secondary) !important;
    font-style: italic;
  }

  // Code elements
  code {
    color: var(--text-color) !important;
    padding: 2px 6px;
    font-family: 'Courier New', monospace;
  }

  pre {
    background: var(--bg-secondary) !important;
    color: var(--text-color) !important;
    padding: 16px;
    border-radius: 8px;
    border: 1px solid var(--border-color-base);
    overflow-x: auto;
    margin-bottom: 16px;

    code {
      background: transparent !important;
      border: none;
      padding: 0;
    }
  }

  // Blockquotes
  blockquote {
    background: var(--bg-secondary) !important;
    color: var(--text-color-secondary) !important;
    border-left: 4px solid var(--primary-color);
    padding: 16px 20px;
    margin: 16px 0;
    border-radius: 0 8px 8px 0;
    font-style: italic;

    p {
      color: var(--text-color-secondary) !important;
      margin-bottom: 0;
    }
  }

  // Tables
  table {
    border-collapse: collapse;
    width: 100%;
    margin-bottom: 16px;
    background: var(--component-background) !important;
    border: 1px solid var(--border-color-base);
    border-radius: 8px;
    overflow: hidden;

    th,
    td {
      color: var(--text-color) !important;
      padding: 12px 16px;
      text-align: left;
      border-bottom: 1px solid var(--border-color-split);
    }

    th {
      background: var(--bg-secondary) !important;
      font-weight: 600;
      color: var(--text-color) !important;
    }

    tr:hover {
      background: var(--item-hover-bg) !important;
    }
  }

  // Images
  img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    margin: 8px 0;
    filter: var(--image-filter, none);
  }

  // Horizontal rules
  hr {
    border: none;
    height: 2px;
    background: var(--border-color-base);
    margin: 24px 0;
    border-radius: 1px;
  }

  // Mark/highlight elements
  mark {
    background: var(--primary-color-light) !important;
    color: var(--text-color) !important;
    padding: 2px 4px;
    border-radius: 3px;
  }

  // Small text
  small {
    color: var(--text-color-tertiary) !important;
    font-size: 0.875em;
  }

  // Other semantic elements
  sub,
  sup {
    color: var(--text-color-secondary) !important;
  }

  del {
    color: var(--error-color) !important;
    text-decoration: line-through;
  }

  ins {
    color: var(--success-color) !important;
    text-decoration: underline;
    background: transparent !important;
  }

  abbr {
    color: var(--text-color) !important;
    border-bottom: 1px dotted var(--text-color-tertiary);
    cursor: help;
  }

  cite {
    color: var(--text-color-secondary) !important;
    font-style: italic;
  }

  time {
    color: var(--text-color-secondary) !important;
  }

  var {
    color: var(--primary-color) !important;
    font-style: italic;
    font-weight: 500;
  }

  kbd,
  samp {
    background: var(--bg-secondary) !important;
    color: var(--text-color) !important;
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    border: 1px solid var(--border-color-base);
  }
}
